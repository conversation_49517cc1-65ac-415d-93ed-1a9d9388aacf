@extends('backend.layouts.app')
@section('title')
    {{ __('company_list') }}
@endsection

@section('content')
    <div class="container-fluid">
        <!-- Statistik Card -->
        <div class="row mb-3">
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-info stat-card" data-url="{{ route('company.index') }}" data-filter="all">
                    <div class="info-box-content">
                        <span class="info-box-text">Total Perusahaan</span>
                        <span class="info-box-number">{{ $totalCompanies }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-success stat-card" data-url="{{ route('company.index', ['status' => 'active']) }}" data-filter="active">
                    <div class="info-box-content">
                        <span class="info-box-text">Perusahaan Aktif</span>
                        <span class="info-box-number">{{ $activeCompanies }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-danger stat-card" data-url="{{ route('company.index', ['status' => 'inactive']) }}" data-filter="inactive">
                    <div class="info-box-content">
                        <span class="info-box-text">Perusahaan Tidak Aktif</span>
                        <span class="info-box-number">{{ $inactiveCompanies }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 col-12">
                <div class="info-box bg-warning stat-card" data-url="{{ route('company.index', ['has_jobs' => 'true']) }}" data-filter="has_jobs">
                    <div class="info-box-content">
                        <span class="info-box-text">Perusahaan Dengan Loker</span>
                        <span class="info-box-number">{{ $companiesWithJobs }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between">
                            <h3 class="card-title line-height-36">{{ __('company_list') }}</h3>
                            <div>
                                <a href="{{ route('company.index', ['view' => 'new']) }}" class="btn bg-info mr-1">
                                    <i class="fas fa-table"></i>&nbsp; {{ __('Tampilan Baru') }}
                                </a>
                                @if (userCan('company.create'))
                                    <a href="{{ route('company.create') }}" class="btn bg-primary"><i
                                            class="fas fa-plus mr-1"></i> {{ __('Tambah Perusahaan') }}
                                    </a>
                                @endif
                                @if (request('keyword') ||
                                        request('ev_status') ||
                                        request('sort_by') ||
                                        request('organization_type') ||
                                        request('industry_type'))
                                    <a href="{{ route('company.index') }}" class="btn bg-danger"><i
                                            class="fas fa-times"></i>&nbsp; {{ __('Hapus Filter') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    {{-- Filter  --}}
                    <form id="formSubmit" action="{{ route('company.index') }}" method="GET" onchange="this.submit();">
                        <div class="card-body border-bottom row">
                            <div class="col-xl-3 col-md-6 col-12">
                                <label>{{ __('search') }}</label>
                                <input name="keyword" type="text" placeholder="{{ __('search') }}" class="form-control"
                                    value="{{ request('keyword') }}">
                            </div>
                            <div class="col-xl-3 col-md-6 col-12">
                                <label>{{ __('organization_type') }}</label>
                                <select name="organization_type" class="form-control select2bs4">
                                    <option value="">
                                        {{ __('all') }}
                                    </option>
                                    @foreach ($organization_types as $organization_type)
                                        <option
                                            {{ request('organization_type') == $organization_type->id ? 'selected' : '' }}
                                            value="{{ $organization_type->id }}">
                                            {{ $organization_type->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-xl-3 col-md-6 col-12">
                                <label>{{ __('industry_type') }}</label>
                                <select name="industry_type" class="form-control select2bs4">
                                    <option value="">
                                        {{ __('all') }}
                                    </option>
                                    @foreach ($industry_types as $industry_type)
                                        <option {{ request('industry_type') == $industry_type->id ? 'selected' : '' }}
                                            value="{{ $industry_type->id }}">
                                            {{ $industry_type->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            {{-- <div class="col-xl-2 col-md-6 col-12">
                                <label>{{ __('email_verification') }}</label>
                                <select name="ev_status" class="form-control select2bs4">
                                    <option value="">
                                        {{ __('all') }}
                                    </option>
                                    <option {{ request('ev_status') == 'true' ? 'selected' : '' }} value="true">
                                        {{ __('verified') }}
                                    </option>
                                    <option {{ request('ev_status') == 'false' ? 'selected' : '' }} value="false">
                                        {{ __('not_verified') }}
                                    </option>
                                </select>
                            </div> --}}
                            <div class="col-xl-3 col-md-6 col-12">
                                <label>{{ __('sort_by') }}</label>
                                <select name="sort_by" class="form-control select2bs4">
                                    <option {{ !request('sort_by') || request('sort_by') == 'latest' ? 'selected' : '' }}
                                        value="latest" selected>
                                        {{ __('latest') }}
                                    </option>
                                    <option {{ request('sort_by') == 'oldest' ? 'selected' : '' }} value="oldest">
                                        {{ __('oldest') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </form>

                    <div class="card-body table-responsive p-0">
                        @include('backend.layouts.partials.message')
                        <table class="ll-table table table-hover text-nowrap">
                            <thead>
                                <tr class="text-center">
                                    <th>{{ __('company') }}</th>
                                    <th>{{ __('active') }} {{ __('job') }}</th>
                                    <th>{{ __('Badan Hukum') }}/{{ __('Kab/Kota') }}</th>
                                    {{-- <th>{{ __('establishment_date') }}</th> --}}
                                    @if (userCan('company.update'))
                                        <th>Jenis Industri</th>
                                        {{-- <th>{{ __('account') }} {{ __('status') }}</th> --}}
                                    @endif
                                    {{-- @if (userCan('company.update'))
                                        <th>{{ __('email_verification') }}</th>
                                    @endif --}}
                                    @if (userCan('company.update'))
                                        <th>{{ __('profile') }} {{ __('status') }}</th>
                                    @endif
                                    @if (userCan('company.update') || userCan('compnay.delete'))
                                        <th width="12%">
                                            {{ __('action') }}
                                        </th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($companies as $company)
                                    <tr>
                                        <td>
                                            <a href='{{ route('company.show', $company->id) }}' class="company">
                                                <img src="{{ $company->logo_url }}" alt="Logo">
                                                <div>
                                                    <h4>{{ formatCompanyName($company) }}
                                                        @if ($company->is_profile_verified)
                                                            <svg style="width: 24px ; height: 24px ; color: green"
                                                                xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                                class="w-6 h-6">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                        @endif
                                                    </h4>
                                                    <p>{{ $company->user->email }}</p>
                                                </div>
                                                <div>


                                                </div>
                                            </a>
                                        </td>
                                        <td>
                                            <a href="{{ route('company.show', $company->id) }}">
                                                {{ $company->active_jobs }} {{ __('active_jobs') }}
                                            </a>
                                        </td>
                                        <td>
                                            <p class="highlight">{{ $company->organization->name }}</p>
                                            <p class="highlight mb-0"><x-svg.table-country />{{ $company->district }}</p>
                                        </td>
                                        <td>
                                            <p class="highlight mb-0">{{ $company->industry->name }}</p>
                                            {{-- <p class="highlight mb-0">{{ $company->establishment_date ? date('j F, Y', strtotime($company->establishment_date)):'-' }}</p> --}}
                                        </td>
                                        @if (userCan('company.update'))
                                            <td tabindex="0">
                                                <a href="#" class="active-status">
                                                    <label class="switch ">
                                                        <input data-id="{{ $company->user_id }}" type="checkbox"
                                                            class="success status-switch"
                                                            {{ $company->user->status == 1 ? 'checked' : '' }}>
                                                        <span class="slider round"></span>
                                                    </label>
                                                    <p style="min-width:70px;"
                                                        class="{{ $company->user->status == 1 ? 'active' : '' }}"
                                                        id="status_{{ $company->user_id }}">
                                                        {{ $company->user->status == 1 ? __('activated') : __('deactivated') }}
                                                    </p>
                                                </a>
                                                <div class="mt-2">
                                                    <a href="{{ route('admin.company.documents', $company) }}">Lihat
                                                        Dokumen</a>
                                                </div>
                                            </td>
                                        @endif
                                        {{-- @if (userCan('company.update'))
                                            <td tabindex="0">
                                                <a href="#" class="active-status">
                                                    <label class="switch ">
                                                        <input data-userid="{{ $company->user_id }}" type="checkbox"
                                                            class="success email-verification-switch"
                                                            {{ $company->user->email_verified_at ? 'checked' : '' }}>
                                                        <span class="slider round"></span>
                                                    </label>
                                                    <p style="min-width:70px" class="{{ $company->user->email_verified_at ? 'active' : '' }}" id="verification_status_{{ $company->user_id }}">{{ $company->user->email_verified_at ? __('verified') : __('unverified') }}</p>
                                                </a>
                                            </td>
                                        @endif --}}
                                        {{-- @if (userCan('company.update') || userCan('compnay.delete'))
                                            <td tabindex="0">
                                                <a href="#" class="active-status">
                                                    <label class="switch ">
                                                        <input data-companyid="{{ $company->id }}" type="checkbox"
                                                            class="success profile-verification-switch"
                                                            {{ $company->is_profile_verified ? 'checked' : '' }}>
                                                        <span class="slider round"></span>
                                                    </label>
                                                    <p style="min-width:70px" class="{{ $company->is_profile_verified ? 'active' : '' }}" id="profile_status_{{ $company->id }}">{{ $company->is_profile_verified ? __('verified') : __('unverified') }}</p>
                                                </a>
                                            </td>
                                        @endif --}}

                                        @if (userCan('company.update') || userCan('compnay.delete'))
                                            <td>
                                                @if (userCan('company.view'))
                                                    <a href="{{ route('company.show', $company->id) }}"
                                                        class="btn ll-btn ll-border-none">
                                                        {{ __('view_profile') }}
                                                        <x-svg.table-btn-arrow />
                                                    </a>
                                                @endif
                                                @if (userCan('company.update'))
                                                    <a href="{{ route('company.edit', $company->id) }}" class="btn ll-p-0">
                                                        <x-svg.table-edit />
                                                    </a>
                                                @endif
                                                @if (userCan('company.delete'))
                                                    <form action="{{ route('company.destroy', $company->id) }}"
                                                        method="POST" class="d-inline">
                                                        @method('DELETE')
                                                        @csrf
                                                        <button
                                                            onclick="return confirm('{{ __('are_you_sure_you_want_to_delete_this_item') }}');"
                                                            class="btn ll-p-0"><x-svg.table-delete /></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </td>
                                        @endif
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10">
                                            <p>{{ __('no_data_found') }}...</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        @if ($companies->count())
                            <div class="mt-3 d-flex justify-content-center">
                                {{ $companies->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <style>
        .switch {
            position: relative;
            display: inline-block;
            width: 35px;
            height: 19px;
        }

        /* Hide default HTML checkbox */
        .switch input {
            display: none;
        }

        /* The slider */
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 15px;
            width: 15px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        input.success:checked+.slider {
            background-color: #28a745;
        }

        input:checked+.slider:before {
            -webkit-transform: translateX(15px);
            -ms-transform: translateX(15px);
            transform: translateX(15px);
        }

        /* Rounded sliders */
        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* Statistik card styling */
        .info-box {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            border-radius: 0.25rem;
            padding: 0;
            min-height: 100px;
            position: relative;
            transition: all .3s;
        }

        .info-box:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.3);
            cursor: pointer;
        }

        .info-box-content {
            padding: 5px 10px;
            text-align: center;
            width: 100%;
        }

        .info-box-text {
            display: block;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-transform: uppercase;
        }

        .info-box-number {
            display: block;
            font-weight: 700;
            font-size: 24px;
        }

        .active-card {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.5) !important;
            border: 2px solid #fff;
        }
    </style>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            // Handle filter card clicks
            $('.stat-card').on('click', function() {
                var url = $(this).data('url');
                window.location.href = url;

                // Highlight active card
                $('.stat-card').removeClass('active-card');
                $(this).addClass('active-card');
            });

            // Highlight active card based on current URL
            var currentUrl = window.location.href;
            if (currentUrl.includes('status=active')) {
                $('.stat-card[data-filter="active"]').addClass('active-card');
            } else if (currentUrl.includes('status=inactive')) {
                $('.stat-card[data-filter="inactive"]').addClass('active-card');
            } else if (currentUrl.includes('has_jobs=true')) {
                $('.stat-card[data-filter="has_jobs"]').addClass('active-card');
            } else {
                $('.stat-card[data-filter="all"]').addClass('active-card');
            }
        });

        $(document).on('change', '.status-switch', function() {
            var status = $(this).prop('checked') == true ? 1 : 0;
            var id = $(this).data('id');
            var switchElement = $(this);

            console.log('Status switch clicked:', {
                id: id,
                status: status,
                switchElement: switchElement
            });

            // Disable switch temporarily
            switchElement.prop('disabled', true);

            $.ajax({
                type: "GET",
                dataType: "json",
                url: '{{ route('company.status.change') }}',
                data: {
                    'status': status,
                    'id': id
                },
                beforeSend: function() {
                    console.log('Sending AJAX request to change status');
                },
                success: function(response) {
                    console.log('Success response:', response);
                    toastr.success(response.message, 'Success');

                    // Update status text
                    if (status == 1) {
                        $(`#status_${id}`).text("{{ __('activated') }}")
                    } else {
                        $(`#status_${id}`).text("{{ __('deactivated') }}")
                    }

                    // Re-enable switch
                    switchElement.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Error response:', xhr, status, error);

                    // Revert switch state
                    switchElement.prop('checked', !switchElement.is(':checked'));

                    // Show error message
                    toastr.error('Terjadi kesalahan saat mengubah status perusahaan: ' + (xhr.responseJSON ? xhr.responseJSON.message : error), 'Error');

                    // Re-enable switch
                    switchElement.prop('disabled', false);
                }
            });
        });
        $('.email-verification-switch').on('change', function() {
            var status = $(this).prop('checked') == true ? 1 : 0;
            var id = $(this).data('userid');
            $.ajax({
                type: "GET",
                dataType: "json",
                url: '{{ route('company.verify.change') }}',
                data: {
                    'status': status,
                    'id': id
                },
                success: function(response) {
                    toastr.success(response.message, 'Success');
                }
            });

            if (status == 1) {
                $(`#verification_status_${id}`).text("{{ __('verified') }}")
            } else {
                $(`#verification_status_${id}`).text("{{ __('unverified') }}")
            }
        });

        $('.profile-verification-switch').on('change', function() {
            var status = $(this).prop('checked') == true ? 1 : 0;
            var id = $(this).data('companyid');
            $.ajax({
                type: "GET",
                dataType: "json",
                url: '{{ route('company.profile.verify.change') }}',
                data: {
                    'status': status,
                    'id': id
                },
                success: function(response) {
                    toastr.success(response.message, 'Success');
                }
            });

            if (status == 1) {
                $(`profile_status_${id}`).text("{{ __('verified') }}")
            } else {
                $(`profile_status_${id}`).text("{{ __('unverified') }}")
            }
        });
    </script>
@endsection
