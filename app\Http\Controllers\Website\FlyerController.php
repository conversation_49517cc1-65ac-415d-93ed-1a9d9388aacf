<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FlyerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        try {
            $company = auth()->user()->company;
            $flyers = \App\Models\Flyer::where('company_id', $company->id)
                ->latest()
                ->paginate(10);

            return view('frontend.pages.company.flyer.index', compact('flyers', 'company'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try {
            $company = auth()->user()->company;
            return view('frontend.pages.company.flyer.create', compact('company'));
        } catch (\Exception $e) {
            flashError('<PERSON><PERSON><PERSON><PERSON> kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'phone_number' => 'required|string|max:14',
                'additional_info' => 'nullable|string|max:500',
                'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120',
            ]);

            $company = auth()->user()->company;

            // Upload image
            $image = $request->file('image');
            $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/flyers'), $imageName);

            // Create flyer
            \App\Models\Flyer::create([
                'company_id' => $company->id,
                'company_name' => formatCompanyName($company),
                'hrd_name' => $company->user->name,
                'phone_number' => $request->phone_number,
                'additional_info' => $request->additional_info,
                'image' => $imageName,
                'status' => 'pending',
            ]);

            flashSuccess('Flyer berhasil diunggah dan sedang menunggu persetujuan admin.');
            return redirect()->route('company.flyer.index');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $company = auth()->user()->company;
            $flyer = \App\Models\Flyer::where('company_id', $company->id)->findOrFail($id);

            return view('frontend.pages.company.flyer.edit', compact('flyer', 'company'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $request->validate([
                'phone_number' => 'required|string|max:14',
                'additional_info' => 'nullable|string|max:500',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            ]);

            $company = auth()->user()->company;
            $flyer = \App\Models\Flyer::where('company_id', $company->id)->findOrFail($id);

            // Upload image if provided
            if ($request->hasFile('image')) {
                // Delete old image
                if ($flyer->image && file_exists(public_path('uploads/flyers/' . $flyer->image))) {
                    unlink(public_path('uploads/flyers/' . $flyer->image));
                }

                // Upload new image
                $image = $request->file('image');
                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $image->move(public_path('uploads/flyers'), $imageName);

                $flyer->image = $imageName;
            }

            // Update flyer
            $flyer->phone_number = $request->phone_number;
            $flyer->additional_info = $request->additional_info;
            $flyer->status = 'pending'; // Reset status to pending after edit
            $flyer->save();

            flashSuccess('Flyer berhasil diperbarui dan sedang menunggu persetujuan admin.');
            return redirect()->route('company.flyer.index');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $company = auth()->user()->company;
            $flyer = \App\Models\Flyer::where('company_id', $company->id)->findOrFail($id);

            // Delete image
            if ($flyer->image && file_exists(public_path('uploads/flyers/' . $flyer->image))) {
                unlink(public_path('uploads/flyers/' . $flyer->image));
            }

            $flyer->delete();

            flashSuccess('Flyer berhasil dihapus.');
            return redirect()->route('company.flyer.index');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }
}
