<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MessageThread extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'company_id',
        'candidate_id',
        'job_id',
        'initiator_id',
        'subject',
        'is_admin_thread',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class)->select('id', 'user_id', 'logo')->with('user:id,name,username');
    }

    public function candidate()
    {
        return $this->belongsTo(Candidate::class)->select('id', 'user_id', 'title', 'photo', 'profession_id')->with('user:id,name,username');
    }

    public function job()
    {
        return $this->belongsTo(Job::class);
    }

    public function initiator()
    {
        return $this->belongsTo(User::class, 'initiator_id');
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function latestMessage()
    {
        return $this->hasOne(Message::class)->latest();
    }

    public function unreadMessages()
    {
        return $this->hasMany(Message::class)->where('read', false);
    }

    /**
     * Get the status badge HTML - Dummy method untuk kompatibilitas tampilan
     */
    public function getStatusBadgeAttribute()
    {
        return '<span class="badge bg-success">Aktif</span>';
    }

    /**
     * Generate a unique ticket number
     */
    public static function generateTicketNumber()
    {
        $prefix = 'TKT';
        $date = date('Ymd');
        $random = mt_rand(1000, 9999);

        return $prefix . '-' . $date . '-' . $random;
    }


}
