<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixCandidateExperiencesTableStructure extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if columns exist and add them if they don't
        if (!Schema::hasColumn('candidate_experiences', 'currently_working')) {
            Schema::table('candidate_experiences', function (Blueprint $table) {
                $table->boolean('currently_working')->default(false)->after('responsibilities');
            });
        }

        if (!Schema::hasColumn('candidate_experiences', 'fresh_graduate')) {
            Schema::table('candidate_experiences', function (Blueprint $table) {
                $table->boolean('fresh_graduate')->default(false)->after('currently_working');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('candidate_experiences', function (Blueprint $table) {
            if (Schema::hasColumn('candidate_experiences', 'fresh_graduate')) {
                $table->dropColumn('fresh_graduate');
            }
            if (Schema::hasColumn('candidate_experiences', 'currently_working')) {
                $table->dropColumn('currently_working');
            }
        });
    }
}
