<?php

namespace App\Http\Livewire\Messenger;

use App\Models\Messenger;
use App\Models\MessengerUser;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;

class ChatBox extends Component
{
    public $selectedUser = null;
    public $messages = [];
    public $chatId = null;
    public $receiverId = null;
    public $userTyping = false;

    protected $listeners = [
        'userSelected' => 'loadUser',
        'messageSent' => 'loadMessages',
        'typingStarted' => 'setUserTyping',
        'typingStopped' => 'unsetUserTyping',
        'echo:chat.{chatId},typing' => 'handleTypingEvent',
        'echo:private:chat.{chatId},message.sent' => 'handleMessageSent'
    ];

    public function getListeners()
    {
        return [
            'userSelected' => 'loadUser',
            'messageSent' => 'loadMessages',
            'typingStarted' => 'setUserTyping',
            'typingStopped' => 'unsetUserTyping',
            "echo:private:chat.{$this->chatId},typing" => 'handleTypingEvent',
            "echo:private:chat.{$this->chatId},message.sent" => 'handleMessageSent'
        ];
    }

    public function loadUser($userId)
    {
        $this->selectedUser = MessengerUser::with(['company.user', 'candidate.user'])->find($userId);
        $this->chatId = $userId;

        if (!$this->selectedUser) {
            return;
        }

        if (auth()->user()->role == 'company') {
            if ($this->selectedUser->candidate && $this->selectedUser->candidate->user) {
                $this->receiverId = $this->selectedUser->candidate->user->id;
            }
        } else {
            if ($this->selectedUser->company && $this->selectedUser->company->user) {
                $this->receiverId = $this->selectedUser->company->user->id;
            }
        }

        $this->loadMessages();
        $this->markAsRead();
    }

    public function loadMessages()
    {
        if (!$this->selectedUser) {
            return;
        }

        $messages = Messenger::where('messenger_user_id', $this->selectedUser->id)
            ->orderBy('created_at', 'asc')
            ->get();

        $this->messages = $messages;
    }

    public function markAsRead()
    {
        if (!$this->selectedUser) {
            return;
        }

        Messenger::where('messenger_user_id', $this->selectedUser->id)
            ->where('to', auth()->id())
            ->where('read', 0)
            ->update(['read' => 1]);

        $this->emit('refreshChatList');
    }

    public function setUserTyping()
    {
        $this->userTyping = true;
    }

    public function unsetUserTyping()
    {
        $this->userTyping = false;
    }

    public function handleTypingEvent($event)
    {
        if ($event['user_id'] != auth()->id()) {
            $this->userTyping = $event['typing'];
        }
    }

    public function handleMessageSent()
    {
        $this->loadMessages();
        $this->markAsRead();
    }

    public function getGroupedMessagesProperty()
    {
        $grouped = [];

        foreach ($this->messages as $message) {
            $date = Carbon::parse($message->created_at)->format('Y-m-d');

            if (!isset($grouped[$date])) {
                $grouped[$date] = [];
            }

            $grouped[$date][] = $message;
        }

        return $grouped;
    }

    public function formatDate($date)
    {
        $carbon = Carbon::parse($date);
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();

        if ($carbon->isSameDay($today)) {
            return 'Today';
        } elseif ($carbon->isSameDay($yesterday)) {
            return 'Yesterday';
        } else {
            return $carbon->format('j F Y');
        }
    }

    public function render()
    {
        return view('livewire.messenger.chat-box');
    }
}
