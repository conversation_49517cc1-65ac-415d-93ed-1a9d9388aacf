<?php

namespace App\Http\Livewire\Messenger;

use App\Events\MessageSent;
use App\Events\TypingEvent;
use App\Models\Messenger;
use Livewire\Component;
use Livewire\WithFileUploads;

class ChatInput extends Component
{
    use WithFileUploads;

    public $message = '';
    public $chatId = null;
    public $receiverId = null;
    public $attachment;
    public $isTyping = false;
    public $typingTimer;

    protected $listeners = [
        'userSelected' => 'setRecipient',
        'emojiSelected' => 'addEmoji'
    ];

    public function setRecipient($chatId)
    {
        $this->chatId = $chatId;

        if (auth()->user()->role == 'company') {
            $messengerUser = \App\Models\MessengerUser::with('candidate.user')->find($chatId);
            if ($messengerUser && $messengerUser->candidate && $messengerUser->candidate->user) {
                $this->receiverId = $messengerUser->candidate->user->id;
            }
        } else {
            $messengerUser = \App\Models\MessengerUser::with('company.user')->find($chatId);
            if ($messengerUser && $messengerUser->company && $messengerUser->company->user) {
                $this->receiverId = $messengerUser->company->user->id;
            }
        }
    }

    public function addEmoji($emoji)
    {
        $this->message .= $emoji;
    }

    public function updatedMessage()
    {
        if (strlen($this->message) > 0 && !$this->isTyping) {
            $this->startTyping();
        } elseif (strlen($this->message) === 0 && $this->isTyping) {
            $this->stopTyping();
        }
    }

    public function startTyping()
    {
        $this->isTyping = true;
        $this->emit('typingStarted');

        event(new TypingEvent(
            auth()->id(),
            $this->receiverId,
            $this->chatId,
            true
        ));
    }

    public function stopTyping()
    {
        $this->isTyping = false;
        $this->emit('typingStopped');

        event(new TypingEvent(
            auth()->id(),
            $this->receiverId,
            $this->chatId,
            false
        ));
    }

    public function sendMessage()
    {
        if (empty($this->message) && !$this->attachment) {
            return;
        }

        if (!$this->chatId || !$this->receiverId) {
            return;
        }

        $messageData = [
            'from' => auth()->id(),
            'to' => $this->receiverId,
            'body' => $this->message ?: 'Sent an attachment',
            'messenger_user_id' => $this->chatId,
            'read' => 0,
        ];

        if ($this->attachment) {
            $path = $this->attachment->store('messenger_attachments', 'public');
            $url = asset('storage/' . $path);
            $fileType = $this->getFileType($this->attachment->getClientOriginalExtension());

            $messageData['attachment'] = [
                'url' => $url,
                'type' => $fileType,
                'name' => $this->attachment->getClientOriginalName(),
                'size' => $this->attachment->getSize(),
            ];

            $messageData['body'] = "[{$fileType}] " . $this->attachment->getClientOriginalName();
        }

        $message = Messenger::create($messageData);

        // Broadcast the message
        event(new MessageSent($message));

        // Stop typing
        $this->stopTyping();

        // Reset form
        $this->reset('message', 'attachment');

        // Emit event to refresh messages
        $this->emit('messageSent');
    }

    private function getFileType($extension)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

        if (in_array(strtolower($extension), $imageExtensions)) {
            return 'image';
        } elseif (in_array(strtolower($extension), $documentExtensions)) {
            return 'document';
        } else {
            return 'file';
        }
    }

    public function render()
    {
        return view('livewire.messenger.chat-input');
    }
}
