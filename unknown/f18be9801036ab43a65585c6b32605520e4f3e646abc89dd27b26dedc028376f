<?php

namespace App\Http\Livewire;

use App\Models\State;
use App\Models\City;
use App\Models\Kecamatan;
use App\Models\Kelurahan;
use Livewire\Component;

class ProvKotaKecKel extends Component
{
    public $states = [];
    public $cities = [];
    public $kecamatan = [];
    public $kelurahan = [];

    public $selectedStateId = null;
    public $selectedCityId = null;
    public $selectedKecamatanId = null;
    public $selectedKelurahanId = null;

    protected $listeners = [
        'getCityByStateId' => 'getCityByStateId',
        'getKecamatanByCityId' => 'getKecamatanByCityId',
        'getKelurahanByKecamatanId' => 'getKelurahanByKecamatanId',
    ];

    public function mount()
    {
        // Set nilai default
        $this->selectedStateId = session('selectedStateId', State::where('name', 'Banten')->value('id'));
        $this->selectedCityId = session('selectedCityId', City::where('name', 'Tangerang Selatan')->value('id'));

        // Inisialisasi data berdasarkan nilai default
        if ($this->selectedStateId) {
            $this->getCityByStateId($this->selectedStateId);
        }
        if ($this->selectedCityId) {
            $this->getKecamatanByCityId($this->selectedCityId);
        }
    }

    public function render()
    {
        // Load states untuk dropdown
        $this->states = State::select('id', 'name')->get();

        return view('livewire.prov-kota-kec-kel');
    }

    public function getCityByStateId($stateId)
    {
        $this->cities = City::where('state_id', $stateId)->select('id', 'name')->get();
        $this->resetSelection('city');
    }

    public function getKecamatanByCityId($cityId)
    {
        $this->kecamatan = Kecamatan::where('city_id', $cityId)->select('id', 'name')->get();
        $this->resetSelection('kecamatan');
    }

    public function getKelurahanByKecamatanId($kecamatanId)
    {
        $this->kelurahan = Kelurahan::where('kecamatan_id', $kecamatanId)->select('id', 'name')->get();
        $this->resetSelection('kelurahan');
    }

    public function updatedSelectedStateId($value)
    {
        session(['selectedStateId' => $value]);
        $this->getCityByStateId($value);
    }

    public function updatedSelectedCityId($value)
    {
        session(['selectedCityId' => $value]);
        $this->getKecamatanByCityId($value);
    }

    public function updatedSelectedKecamatanId($value)
    {
        session(['selectedKecamatanId' => $value]);
        $this->getKelurahanByKecamatanId($value);
    }

    public function updatedSelectedKelurahanId($value)
    {
        session(['selectedKelurahanId' => $value]);
    }

    private function resetSelection($level)
    {
        // Reset pilihan berdasarkan level
        if ($level === 'city') {
            $this->selectedCityId = null;
            $this->selectedKecamatanId = null;
            $this->selectedKelurahanId = null;
            $this->kecamatan = [];
            $this->kelurahan = [];
        } elseif ($level === 'kecamatan') {
            $this->selectedKecamatanId = null;
            $this->selectedKelurahanId = null;
            $this->kelurahan = [];
        } elseif ($level === 'kelurahan') {
            $this->selectedKelurahanId = null;
        }
    }
}
