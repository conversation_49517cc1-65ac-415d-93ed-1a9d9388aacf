<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class ValidationController extends Controller
{
    /**
     * Cek apakah NIK sudah digunakan
     */
    public function checkNik(Request $request)
    {
        $nik = $request->nik;
        $user_id = $request->user_id ?? null;

        $query = User::where('nik', $nik);

        // Jika ada user_id, exclude user tersebut dari pengecekan (untuk edit)
        if ($user_id) {
            $query->where('id', '!=', $user_id);
        }

        $exists = $query->exists();

        return response()->json(['exists' => $exists]);
    }

    /**
     * Cek apakah email sudah digunakan
     */
    public function checkEmail(Request $request)
    {
        $email = $request->email;
        $user_id = $request->user_id ?? null;

        $query = User::where('email', $email);

        // Jika ada user_id, exclude user tersebut dari pengecekan (untuk edit)
        if ($user_id) {
            $query->where('id', '!=', $user_id);
        }

        $exists = $query->exists();

        return response()->json(['exists' => $exists]);
    }

    /**
     * Cek apakah nomor HP sudah digunakan
     */
    public function checkPhone(Request $request)
    {
        $no_hp = $request->no_hp;
        $user_id = $request->user_id ?? null;

        $query = User::where('no_hp', $no_hp);

        // Jika ada user_id, exclude user tersebut dari pengecekan (untuk edit)
        if ($user_id) {
            $query->where('id', '!=', $user_id);
        }

        $exists = $query->exists();

        return response()->json(['exists' => $exists]);
    }
}
