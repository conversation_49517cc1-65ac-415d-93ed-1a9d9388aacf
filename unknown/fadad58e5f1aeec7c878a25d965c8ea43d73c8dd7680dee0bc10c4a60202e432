<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class StartWebSocketServer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'websockets:start';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the WebSockets server';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('WebSockets Server Information');
        $this->info('------------------------------');
        $this->info('For development, you can use Pusher directly:');
        $this->info('1. Sign up for a free Pusher account at https://pusher.com/');
        $this->info('2. Create a new app in the Pusher dashboard');
        $this->info('3. Update your .env file with the Pusher credentials:');
        $this->info('   PUSHER_APP_ID=your-app-id');
        $this->info('   PUSHER_APP_KEY=your-app-key');
        $this->info('   PUSHER_APP_SECRET=your-app-secret');
        $this->info('   PUSHER_APP_CLUSTER=your-app-cluster');
        $this->info('');
        $this->info('For production, you can use Laravel WebSockets:');
        $this->info('1. Install the package: composer require beyondcode/laravel-websockets');
        $this->info('2. Publish the config: php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="config"');
        $this->info('3. Run the migrations: php artisan migrate');
        $this->info('4. Start the WebSockets server: php artisan websockets:serve');

        // Start the Laravel development server
        $this->info('');
        $this->info('Starting Laravel development server...');
        $this->call('serve');
    }
}
