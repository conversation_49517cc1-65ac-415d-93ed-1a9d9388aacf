<?php

namespace App\Notifications\Website\Candidate;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationStatusNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public $user;
    public $company;
    public $job;
    public $status;
    public $interview_details;

    public function __construct($user, $company, $job, $status, $interview_details = null)
    {
        $this->user = $user;
        $this->company = $company;
        $this->job = $job;
        $this->status = $status;
        $this->interview_details = $interview_details;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mailMessage = (new MailMessage)
            ->greeting('Yth, ' . $this->user->name)
            ->subject("Status Lamaran Anda untuk {$this->job->title} di {$this->company->name} Telah Diperbarui");

        switch ($this->status) {
            case 'Interview':
                $mailMessage->line("Selamat! Lamaran Anda untuk posisi {$this->job->title} di {$this->company->name} telah masuk ke tahap interview.");

                if ($this->interview_details) {
                    $formattedDate = \Carbon\Carbon::parse($this->interview_details['date'])->locale('id')->isoFormat('D MMMM Y');
                    $formattedTime = \Carbon\Carbon::parse($this->interview_details['time'])->format('H:i') . ' WIB';
                    $mailMessage->line("Detail Interview:");
                    $mailMessage->line("Tanggal: {$formattedDate}");
                    $mailMessage->line("Waktu: {$formattedTime}");
                    $mailMessage->line("Lokasi: {$this->interview_details['location']}");

                    if (!empty($this->interview_details['notes'])) {
                        $mailMessage->line("Catatan: {$this->interview_details['notes']}");
                    }
                }
                break;

            case 'Diterima':
                $mailMessage->line("Selamat! Anda telah diterima untuk posisi {$this->job->title} di {$this->company->name}.");
                $mailMessage->line("Tim HR akan menghubungi Anda untuk langkah selanjutnya.");
                break;

            case 'Ditolak':
                $mailMessage->line("Terima kasih atas minat Anda pada posisi {$this->job->title} di {$this->company->name}.");
                $mailMessage->line("Setelah meninjau lamaran Anda dengan seksama, kami memutuskan untuk melanjutkan dengan kandidat lain yang lebih sesuai dengan kebutuhan kami saat ini.");
                $mailMessage->line("Kami menghargai waktu dan usaha Anda dalam proses lamaran ini.");
                break;

            default:
                $mailMessage->line("Status lamaran Anda untuk posisi {$this->job->title} di {$this->company->name} telah diperbarui menjadi {$this->status}.");
                break;
        }

        $mailMessage->action('Lihat Lamaran', route('candidate.appliedjob'))
            ->line('Terima kasih telah menggunakan ' . config('app.name') . '.');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $title = '';

        switch ($this->status) {
            case 'Interview':
                $title = "Lamaran Anda untuk posisi {$this->job->title} di {$this->company->name} telah masuk ke tahap interview.";
                break;

            case 'Diterima':
                $title = "Selamat! Anda telah diterima untuk posisi {$this->job->title} di {$this->company->name}.";
                break;

            case 'Ditolak':
                $title = "Lamaran Anda untuk posisi {$this->job->title} di {$this->company->name} tidak diterima.";
                break;

            default:
                $title = "Status lamaran Anda untuk posisi {$this->job->title} di {$this->company->name} telah diperbarui.";
                break;
        }

        return [
            'title' => $title,
            'url' => route('candidate.appliedjob'),
            'status' => $this->status,
            'job_id' => $this->job->id,
            'interview_details' => $this->interview_details,
        ];
    }
}
