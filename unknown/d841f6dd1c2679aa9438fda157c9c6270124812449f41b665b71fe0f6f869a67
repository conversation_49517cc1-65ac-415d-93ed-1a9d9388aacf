<?php

namespace App\Http\Controllers\Website;

use App\Events\NewFeedbackEvent;
use App\Http\Controllers\Controller;
use App\Models\Feedback;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FeedbackController extends Controller
{
    /**
     * Display the feedback form page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $setting = Setting::first();

        return view('frontend.pages.feedback', compact('setting'));
    }

    /**
     * Store a newly created feedback in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string',
            'rating' => 'required|integer|min:1|max:5',
        ]);

        $user_type = 'guest';
        $user_id = null;

        if (Auth::check()) {
            $user = Auth::user();
            $user_id = $user->id;
            $user_type = $user->role;
        }

        $feedback = Feedback::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->message,
            'rating' => $request->rating,
            'user_type' => $user_type,
            'user_id' => $user_id,
        ]);

        // Trigger event for new feedback
        event(new NewFeedbackEvent($feedback));

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Terima kasih atas saran dan masukan Anda!'
            ]);
        }

        flashSuccess('Terima kasih atas saran dan masukan Anda!');
        return redirect()->route('website.feedback');
    }
}
