<?php return array (
  'providers' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    2 => 'Illuminate\\Bus\\BusServiceProvider',
    3 => 'Illuminate\\Cache\\CacheServiceProvider',
    4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    5 => 'Illuminate\\Cookie\\CookieServiceProvider',
    6 => 'Illuminate\\Database\\DatabaseServiceProvider',
    7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    10 => 'Illuminate\\Hashing\\HashServiceProvider',
    11 => 'Illuminate\\Mail\\MailServiceProvider',
    12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    15 => 'Illuminate\\Queue\\QueueServiceProvider',
    16 => 'Illuminate\\Redis\\RedisServiceProvider',
    17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    18 => 'Illuminate\\Session\\SessionServiceProvider',
    19 => 'Illuminate\\Translation\\TranslationServiceProvider',
    20 => 'Illuminate\\Validation\\ValidationServiceProvider',
    21 => 'Illuminate\\View\\ViewServiceProvider',
    22 => 'Anhskohbo\\NoCaptcha\\NoCaptchaServiceProvider',
    23 => 'Arcanedev\\LogViewer\\LogViewerServiceProvider',
    24 => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    25 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    26 => 'Barryvdh\\Debugbar\\ServiceProvider',
    27 => 'Barryvdh\\DomPDF\\ServiceProvider',
    28 => 'BeyondCode\\LaravelWebSockets\\WebSocketsServiceProvider',
    29 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    30 => 'ImLiam\\EnvironmentSetCommand\\EnvironmentSetCommandServiceProvider',
    31 => 'October\\Rain\\Config\\ServiceProvider',
    32 => 'DGvai\\SSLCommerz\\SSLCommerzServiceProvider',
    33 => 'Getsolaris\\LaravelMakeService\\LaravelMakeServiceProvider',
    34 => 'Intervention\\Image\\ImageServiceProvider',
    35 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    36 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    37 => '\\Ladumor\\LaravelPwa\\PWAServiceProvider',
    38 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    39 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    40 => 'Laravel\\Tinker\\TinkerServiceProvider',
    41 => 'Laravel\\Ui\\UiServiceProvider',
    42 => 'Laravolt\\Avatar\\ServiceProvider',
    43 => 'Livewire\\LivewireServiceProvider',
    44 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    45 => 'Mediconesystems\\LivewireDatatables\\LivewireDatatablesServiceProvider',
    46 => 'Mollie\\Laravel\\MollieServiceProvider',
    47 => 'Carbon\\Laravel\\ServiceProvider',
    48 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    49 => 'Termwind\\Laravel\\TermwindServiceProvider',
    50 => 'Nwidart\\Modules\\LaravelModulesServiceProvider',
    51 => 'PowerComponents\\LivewirePowerGrid\\Providers\\PowerGridServiceProvider',
    52 => 'Reedware\\LaravelRelationJoins\\LaravelRelationJoinServiceProvider',
    53 => 'secondnetwork\\TablerIcons\\BladeTablerIconsServiceProvider',
    54 => 'Sentry\\Laravel\\ServiceProvider',
    55 => 'Sentry\\Laravel\\Tracing\\ServiceProvider',
    56 => 'Spatie\\Backup\\BackupServiceProvider',
    57 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    58 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    59 => 'Spatie\\Permission\\PermissionServiceProvider',
    60 => 'Spatie\\SignalAwareCommand\\SignalAwareCommandServiceProvider',
    61 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    62 => 'Srmklive\\PayPal\\Providers\\PayPalServiceProvider',
    63 => 'Stevebauman\\Location\\LocationServiceProvider',
    64 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    65 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    66 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    67 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    68 => 'Barryvdh\\DomPDF\\ServiceProvider',
    69 => 'Livewire\\LivewireServiceProvider',
    70 => 'Spatie\\Permission\\PermissionServiceProvider',
    71 => 'Srmklive\\PayPal\\Providers\\PayPalServiceProvider',
    72 => 'Stevebauman\\Location\\LocationServiceProvider',
    73 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    74 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    75 => 'App\\Providers\\AppServiceProvider',
    76 => 'App\\Providers\\AuthServiceProvider',
    77 => 'App\\Providers\\BroadcastServiceProvider',
    78 => 'App\\Providers\\EventServiceProvider',
    79 => 'App\\Providers\\RouteServiceProvider',
    80 => 'App\\Providers\\BladeServiceProvider',
    81 => 'Intervention\\Image\\ImageServiceProvider',
    82 => 'Ladumor\\LaravelPwa\\PWAServiceProvider',
    83 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    84 => 'BeyondCode\\LaravelWebSockets\\WebSocketsServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Cookie\\CookieServiceProvider',
    2 => 'Illuminate\\Database\\DatabaseServiceProvider',
    3 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    4 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    5 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    6 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    7 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    8 => 'Illuminate\\Session\\SessionServiceProvider',
    9 => 'Illuminate\\View\\ViewServiceProvider',
    10 => 'Anhskohbo\\NoCaptcha\\NoCaptchaServiceProvider',
    11 => 'Arcanedev\\LogViewer\\LogViewerServiceProvider',
    12 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    13 => 'Barryvdh\\Debugbar\\ServiceProvider',
    14 => 'Barryvdh\\DomPDF\\ServiceProvider',
    15 => 'BeyondCode\\LaravelWebSockets\\WebSocketsServiceProvider',
    16 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    17 => 'ImLiam\\EnvironmentSetCommand\\EnvironmentSetCommandServiceProvider',
    18 => 'October\\Rain\\Config\\ServiceProvider',
    19 => 'DGvai\\SSLCommerz\\SSLCommerzServiceProvider',
    20 => 'Getsolaris\\LaravelMakeService\\LaravelMakeServiceProvider',
    21 => 'Intervention\\Image\\ImageServiceProvider',
    22 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    23 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    24 => '\\Ladumor\\LaravelPwa\\PWAServiceProvider',
    25 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    26 => 'Laravel\\Ui\\UiServiceProvider',
    27 => 'Laravolt\\Avatar\\ServiceProvider',
    28 => 'Livewire\\LivewireServiceProvider',
    29 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    30 => 'Mediconesystems\\LivewireDatatables\\LivewireDatatablesServiceProvider',
    31 => 'Mollie\\Laravel\\MollieServiceProvider',
    32 => 'Carbon\\Laravel\\ServiceProvider',
    33 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    34 => 'Termwind\\Laravel\\TermwindServiceProvider',
    35 => 'Nwidart\\Modules\\LaravelModulesServiceProvider',
    36 => 'PowerComponents\\LivewirePowerGrid\\Providers\\PowerGridServiceProvider',
    37 => 'Reedware\\LaravelRelationJoins\\LaravelRelationJoinServiceProvider',
    38 => 'secondnetwork\\TablerIcons\\BladeTablerIconsServiceProvider',
    39 => 'Sentry\\Laravel\\ServiceProvider',
    40 => 'Sentry\\Laravel\\Tracing\\ServiceProvider',
    41 => 'Spatie\\Backup\\BackupServiceProvider',
    42 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    43 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    44 => 'Spatie\\Permission\\PermissionServiceProvider',
    45 => 'Spatie\\SignalAwareCommand\\SignalAwareCommandServiceProvider',
    46 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    47 => 'Srmklive\\PayPal\\Providers\\PayPalServiceProvider',
    48 => 'Stevebauman\\Location\\LocationServiceProvider',
    49 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    50 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    51 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    52 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    53 => 'Barryvdh\\DomPDF\\ServiceProvider',
    54 => 'Livewire\\LivewireServiceProvider',
    55 => 'Spatie\\Permission\\PermissionServiceProvider',
    56 => 'Srmklive\\PayPal\\Providers\\PayPalServiceProvider',
    57 => 'Stevebauman\\Location\\LocationServiceProvider',
    58 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    59 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    60 => 'App\\Providers\\AppServiceProvider',
    61 => 'App\\Providers\\AuthServiceProvider',
    62 => 'App\\Providers\\BroadcastServiceProvider',
    63 => 'App\\Providers\\EventServiceProvider',
    64 => 'App\\Providers\\RouteServiceProvider',
    65 => 'App\\Providers\\BladeServiceProvider',
    66 => 'Intervention\\Image\\ImageServiceProvider',
    67 => 'Ladumor\\LaravelPwa\\PWAServiceProvider',
    68 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    69 => 'BeyondCode\\LaravelWebSockets\\WebSocketsServiceProvider',
  ),
  'deferred' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastManager' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Factory' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Broadcaster' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\QueueingDispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\BatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\DatabaseBatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'cache' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.store' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.psr6' => 'Illuminate\\Cache\\CacheServiceProvider',
    'memcached.connector' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Cache\\RateLimiter' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Foundation\\Console\\AboutCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\ClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\ForgetCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ClearCompiledCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Auth\\Console\\ClearResetsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigShowCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DbCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\MonitorCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\PruneCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\ShowCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\TableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\WipeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\DownCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentDecryptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentEncryptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\KeyGenerateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\OptimizeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\OptimizeClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\PackageDiscoverCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\PruneStaleTagsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ListFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\FlushFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ForgetFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ListenCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\MonitorCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\PruneBatchesCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\PruneFailedJobsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RestartCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RetryCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RetryBatchCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\WorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DumpCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Seeds\\SeedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleFinishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleRunCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleClearCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleTestCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleWorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleInterruptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\ShowModelCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StorageLinkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StorageUnlinkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\UpCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\CacheTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\CastMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ChannelListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ChannelMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ComponentMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConsoleMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Routing\\Console\\ControllerMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\DocsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventGenerateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ExceptionMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Factories\\FactoryMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\JobMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\LangPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ListenerMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\MailMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Routing\\Console\\MiddlewareMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ModelMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\NotificationMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Notifications\\Console\\NotificationTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ObserverMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\PolicyMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ProviderMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\FailedTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\TableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\BatchesTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RequestMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ResourceMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RuleMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ScopeMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Seeds\\SeederMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Session\\Console\\SessionTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ServeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StubPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\TestMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\VendorPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migrator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.repository' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.creator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\FreshCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\InstallCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RefreshCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\ResetCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RollbackCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\StatusCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'composer' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'hash' => 'Illuminate\\Hashing\\HashServiceProvider',
    'hash.driver' => 'Illuminate\\Hashing\\HashServiceProvider',
    'mail.manager' => 'Illuminate\\Mail\\MailServiceProvider',
    'mailer' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Mail\\Markdown' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Contracts\\Pipeline\\Hub' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'pipeline' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'queue' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.connection' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.failer' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.listener' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.worker' => 'Illuminate\\Queue\\QueueServiceProvider',
    'redis' => 'Illuminate\\Redis\\RedisServiceProvider',
    'redis.connection' => 'Illuminate\\Redis\\RedisServiceProvider',
    'auth.password' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'auth.password.broker' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'translator' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'translation.loader' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'validator' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'validation.presence' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Illuminate\\Contracts\\Validation\\UncompromisedVerifier' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Arcanedev\\LogViewer\\Contracts\\LogViewer' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogLevels' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogStyler' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogMenu' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\Filesystem' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\Factory' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogChecker' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Laravel\\Socialite\\Contracts\\Factory' => 'Laravel\\Socialite\\SocialiteServiceProvider',
    'command.tinker' => 'Laravel\\Tinker\\TinkerServiceProvider',
  ),
  'when' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastServiceProvider' => 
    array (
    ),
    'Illuminate\\Bus\\BusServiceProvider' => 
    array (
    ),
    'Illuminate\\Cache\\CacheServiceProvider' => 
    array (
    ),
    'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider' => 
    array (
    ),
    'Illuminate\\Hashing\\HashServiceProvider' => 
    array (
    ),
    'Illuminate\\Mail\\MailServiceProvider' => 
    array (
    ),
    'Illuminate\\Pipeline\\PipelineServiceProvider' => 
    array (
    ),
    'Illuminate\\Queue\\QueueServiceProvider' => 
    array (
    ),
    'Illuminate\\Redis\\RedisServiceProvider' => 
    array (
    ),
    'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider' => 
    array (
    ),
    'Illuminate\\Translation\\TranslationServiceProvider' => 
    array (
    ),
    'Illuminate\\Validation\\ValidationServiceProvider' => 
    array (
    ),
    'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider' => 
    array (
    ),
    'Laravel\\Socialite\\SocialiteServiceProvider' => 
    array (
    ),
    'Laravel\\Tinker\\TinkerServiceProvider' => 
    array (
    ),
  ),
);