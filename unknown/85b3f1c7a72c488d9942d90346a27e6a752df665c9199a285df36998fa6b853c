<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Feedback extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'user_type',
        'user_id',
        'message',
        'rating',
        'is_read'
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'rating' => 'integer',
    ];

    protected $appends = [
        'rating_stars',
    ];

    /**
     * Get the user that owns the feedback.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the rating as stars HTML.
     *
     * @return string
     */
    public function getRatingStarsAttribute()
    {
        $stars = '';
        $rating = $this->rating;

        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $stars .= '<i class="ph-star-fill text-warning"></i>';
            } else {
                $stars .= '<i class="ph-star text-muted"></i>';
            }
        }

        return $stars . ' <span class="ml-1">' . $rating . '/5</span>';
    }
}
