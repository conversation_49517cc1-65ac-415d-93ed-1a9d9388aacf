<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CandidateMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Pastikan user sudah login
        if (!auth('user')->check()) {
            return redirect()->route('login');
        }

        if (authUser()->role == 'candidate') {
            return $next($request);
        }

        if (authUser()->role == 'company') {
            return redirect()->route('company.dashboard');
        }

        return redirect()->route('login');
    }
}
