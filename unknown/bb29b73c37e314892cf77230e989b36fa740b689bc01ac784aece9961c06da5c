<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Flyer extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'company_name',
        'hrd_name',
        'phone_number',
        'additional_info',
        'image',
        'status',
        'rejection_reason',
        'job_id',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function job()
    {
        return $this->belongsTo(Job::class);
    }

    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return asset('uploads/flyers/' . $this->image);
        }

        return asset('backend/image/default.png');
    }
}
