<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;

class Messenger extends Model
{
    protected $table = 'messengers';

    /**
     * Override the query method to handle the case when the table doesn't exist
     */
    public function newQuery()
    {
        // Redirect to new message system
        if (request()->is('company/message/candidate') || request()->is('*/message/*')) {
            return app(Message::class)->newQuery();
        }

        return parent::newQuery();
    }

    /**
     * Override the newEloquentBuilder method to handle the case when the table doesn't exist
     */
    public function newEloquentBuilder($query)
    {
        if (!Schema::hasTable($this->getTable())) {
            return app(Message::class)->newEloquentBuilder($query);
        }

        return parent::newEloquentBuilder($query);
    }

    /**
     * Override the save method to handle the case when the table doesn't exist
     */
    public function save(array $options = [])
    {
        // Redirect to new message system
        if (request()->is('company/message/candidate')) {
            return true;
        }

        return parent::save($options);
    }
}
