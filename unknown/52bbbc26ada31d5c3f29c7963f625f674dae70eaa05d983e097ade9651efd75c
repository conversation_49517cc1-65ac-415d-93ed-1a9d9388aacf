<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class CandidateDataTableController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        // Log untuk debugging pagination
        \Log::info('DataTables Request', [
            'start' => $request->input('start'),
            'length' => $request->input('length'),
            'draw' => $request->input('draw'),
            'search' => $request->input('search.value'),
            'order' => $request->input('order')
        ]);

        // Query dasar
        $query = Candidate::select([
                'candidates.id',
                'candidates.user_id',
                'candidates.gender',
                'candidates.education_id',
                'candidates.photo',
                'candidates.created_at',
                'users.name as user_name',
                'users.email as user_email',
                'users.no_hp as user_phone',
                'users.nik as user_nik',
                'users.status as user_status',
                'education_translations.name as education_name'
            ])
            ->join('users', 'candidates.user_id', '=', 'users.id')
            ->leftJoin('education', 'candidates.education_id', '=', 'education.id')
            ->leftJoin('education_translations', function($join) {
                $join->on('education.id', '=', 'education_translations.education_id')
                    ->where('education_translations.locale', app()->getLocale());
            })
            ->withCount(['appliedJobs' => function($query) {
                $query->whereNotNull('candidate_job.id');
            }]);

        // Filter berdasarkan kota/kabupaten
        if ($request->has('city') && !empty($request->city)) {
            $query->where('users.kabupaten_kota', $request->city);
        }

        // Filter berdasarkan jenis kelamin
        if ($request->has('gender') && !empty($request->gender)) {
            $query->where('candidates.gender', $request->gender);
        }

        // Filter berdasarkan pendidikan
        if ($request->has('education') && !empty($request->education)) {
            $query->where('candidates.education_id', $request->education);
        }

        // Filter berdasarkan status
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status == 'active') {
                $query->where('users.status', 1);
            } elseif ($request->status == 'inactive') {
                $query->where('users.status', 0);
            }
        }

        // Filter berdasarkan pencarian global DataTables
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function($q) use ($searchValue) {
                $q->where('users.name', 'LIKE', "%{$searchValue}%")
                  ->orWhere('users.email', 'LIKE', "%{$searchValue}%")
                  ->orWhere('users.nik', 'LIKE', "%{$searchValue}%")
                  ->orWhere('users.no_hp', 'LIKE', "%{$searchValue}%");
            });
        }

        // Urutkan berdasarkan waktu terbaru
        $query->orderBy('candidates.created_at', 'desc');

        // Buat DataTables
        $dataTable = DataTables::of($query);

        // Tambahkan kolom-kolom
        $dataTable->addColumn('checkbox', function ($candidate) {
            return '<div class="form-check">
                <input class="form-check-input candidate-checkbox" type="checkbox" value="' . $candidate->id . '" data-user-id="' . $candidate->user_id . '">
                <label class="form-check-label"></label>
            </div>';
        });

        $dataTable->addColumn('name', function ($candidate) {
            $photoUrl = $candidate->photo ? asset($candidate->photo) : asset('backend/image/default.png');

            $html = '<div class="candidate">
                <img src="' . $photoUrl . '" alt="Foto Profil">
                <div>
                    <h4>' . $candidate->user_name . '</h4>
                    <p><i class="ph ph-envelope-simple"></i> ' . $candidate->user_email . '</p>
                    <p><i class="ph ph-phone"></i> ' . $candidate->user_phone . '</p>';

            if ($candidate->user_nik) {
                $html .= '<p><i class="ph ph-identification-card"></i> ' . $candidate->user_nik . '</p>';
            }

            $html .= '</div></div>';

            return $html;
        });

        $dataTable->addColumn('education', function ($candidate) {
            return '<p class="highlight">' . ($candidate->education_name ?? '-') . '</p>';
        });

        $dataTable->addColumn('applied_jobs', function ($candidate) {
            if ($candidate->applied_jobs_count > 0) {
                return '<button type="button" class="btn btn-sm btn-info view-applied-jobs" data-id="' . $candidate->id . '">' . $candidate->applied_jobs_count . ' Pekerjaan</button>';
            } else {
                return '<span class="badge badge-secondary">Belum melamar</span>';
            }
        });

        $dataTable->addColumn('status', function ($candidate) {
            $checked = $candidate->user_status == 1 ? 'checked' : '';
            return '<div class="form-check form-switch">
                <input class="form-check-input candidate-status" type="checkbox" ' . $checked . ' data-id="' . $candidate->user_id . '" role="switch" id="candidate_status_' . $candidate->user_id . '">
                <label class="form-check-label" for="candidate_status_' . $candidate->user_id . '"></label>
            </div>';
        });

        $dataTable->addColumn('action', function ($candidate) {
            $viewBtn = '<button type="button" class="btn btn-sm btn-info view-profile mr-1" data-id="' . $candidate->id . '" data-toggle="tooltip" title="Lihat Profil"><i class="fas fa-eye"></i></button>';
            $editBtn = '<a href="' . route('candidate.edit', $candidate->id) . '" class="btn btn-sm btn-warning mr-1" data-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>';
            $deleteBtn = '<form action="' . route('candidate.destroy', $candidate->id) . '" method="POST" class="d-inline delete-form">
                ' . method_field('DELETE') . '
                <input type="hidden" name="_token" value="' . csrf_token() . '">
                <button type="button" class="btn btn-sm btn-danger delete-btn" data-toggle="tooltip" title="Hapus"><i class="fas fa-trash"></i></button>
            </form>';

            return '<div class="action-buttons">' . $viewBtn . $editBtn . $deleteBtn . '</div>';
        });

        // Set kolom yang menggunakan HTML
        $dataTable->rawColumns(['checkbox', 'name', 'education', 'applied_jobs', 'status', 'action']);

        return $dataTable->make(true);
    }
}
