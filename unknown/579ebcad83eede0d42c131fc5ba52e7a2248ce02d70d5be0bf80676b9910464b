<?php

namespace App\Services\Admin\Job;

use App\Models\Job;

class JobListService
{
    /**
     * Get job list
     */
    public function execute($request): mixed
    {
        $query = Job::query()->with('role', 'category', 'salary_type', 'company');

        // keyword
        if ($request->title && $request->title != null) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'LIKE', "%$request->title%")
                  ->orWhereHas('category.translations', function($q) use ($request) {
                      $q->where('job_category_translations.name', 'LIKE', "%$request->title%");
                  })
                  ->orWhereHas('role', function($q) use ($request) {
                      $q->where('name', 'LIKE', "%$request->title%");
                  });
            });
        }

        // status
        if ($request->status && $request->status != null) {
            if ($request->status != 'all') {
                $query->where('status', $request->status);
            }
        }

        // job_category
        if ($request->job_category && $request->job_category != null) {
            $query->where('category_id', $request->job_category);
        }

        // experience
        if ($request->experience && $request->experience != null) {
            $query->whereHas('experience', function ($q) use ($request) {
                $q->where('slug', $request->experience);
            });
        }

        // job_type
        if ($request->job_type && $request->job_type != null) {
            $query->whereHas('job_type', function ($q) use ($request) {
                $q->where('slug', $request->job_type);
            });
        }

        // Gender filter
        if ($request->gender && $request->gender != null) {
            if ($request->gender !== 'both') {
                $query->where('gender', $request->gender);
            }
        }

        // filter_by (legacy support)
        if ($request->filter_by && $request->filter_by != null) {
            $query->where('status', $request->filter_by);
        }

        // status filter (new)
        if ($request->status && $request->status != null) {
            $query->where('status', $request->status);
        }

        // salary sorting
        if ($request->sort_salary && $request->sort_salary != null) {
            if ($request->sort_salary == 'highest') {
                $query->orderBy('max_salary', 'desc');
            } elseif ($request->sort_salary == 'lowest') {
                $query->orderBy('min_salary', 'asc');
            }
        }

        // Sort by date
        if ($request->sort_by && $request->sort_by != null) {
            if ($request->sort_by == 'latest') {
                $query->latest();
            } elseif ($request->sort_by == 'oldest') {
                $query->oldest();
            }
        } else {
            $query->latest(); // Default sorting by latest
        }

        $jobs = $query->withoutEdited()->with(['experience', 'job_type', 'category', 'allAppliedJobs'])->paginate(15);
        $jobs->appends($request->all());

        return $jobs;
    }
}
