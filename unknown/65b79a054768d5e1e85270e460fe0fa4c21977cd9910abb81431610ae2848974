<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Admin;
use App\Models\Setting;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use F9Web\ApiResponseHelpers;
use App\Models\VerificationCode;
use App\Http\Controllers\Controller;
use App\Http\Resources\Candidate\CandidateResource;
use App\Http\Resources\Company\CompanyResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Notifications\Api\ResetPassword;
use Illuminate\Support\Facades\Notification;
use App\Notifications\CompanyCreatedNotification;
use App\Notifications\CandidateCreateNotification;
use App\Notifications\Admin\NewUserRegisteredNotification;
use App\Notifications\CompanyCreateApprovalPendingNotification;
use App\Notifications\CandidateCreateApprovalPendingNotification;
use Firebase\Auth\Token\Exception\InvalidToken;

class AuthController extends Controller
{
    use ApiResponseHelpers;

    /**
     * Handle an authentication attempt.
     *
     * @param  \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request){
        $request->validate([
            'email' => 'required|email',
            'password' => 'required'
        ]);

        if (Auth::attempt(['email' => $request->email, 'password' => $request->password])) {
            $user = Auth::user();
            $token =  $user->createToken('job-pilot')->plainTextToken;

            return $this->respondWithSuccess([
                'data' => [
                    'token' => $token,
                    'message' => 'Login Berhasil',
                    'user' => $user->role == 'candidate' ? new CandidateResource($user->candidate) : new CompanyResource($user->company)
                ]
            ]);
        }else{
            return $this->respondUnAuthenticated('Invalid Credentials');
        }
    }

    /**
     * Retrieve authenticated user's information.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * If the user is authenticated via Sanctum, returns a JSON response with the user's data,
     * including a bearer token and a message indicating successful data retrieval.
     * If not authenticated, returns an unauthenticated response.
     */
    public function getUserInfo(Request $request)
    {
        $user = auth('sanctum')->user();

        if($user)
        {
            $token = $request->bearerToken();

            return $this->respondWithSuccess([
                'data' => [
                    'token' => $request->bearerToken(),
                    'message' => 'Data pengguna berhasil diterima',
                    'user' => $user->role == 'candidate' ? new CandidateResource($user->candidate) : new CompanyResource($user->company)

                ]
            ]);
        }else{
                return $this->respondUnAuthenticated('Pengguna Tidak Terotentikasi');

        }
    }

    /**
     * Register a new user and send a notification to the admin.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * If the validation is successful, it will create a new user and send notifications to the admins.
     * If the email is already registered, it will return an error response.
     * If the validation fails, it will return an error response.
     */
    public function register(Request $request){
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ];

        // Tambahkan validasi untuk field perusahaan jika role adalah company
        if ($request->role == 'company') {
            $rules['nama_hrd'] = 'required|string|max:100';
            $rules['jabatan'] = 'required|string|max:100';
        }

        $request->validate($rules);

        $newUsername = Str::slug($request->name);
        $oldUserName = User::where('username', $newUsername)->first();

        if ($oldUserName) {
            $username = Str::slug($newUsername) . '_' . Str::random(5);
        } else {
            $username = Str::slug($newUsername);
        }

        $userData = [
            'role' => $request->role == 'candidate' ? 'candidate' : 'company',
            'name' => $request->name,
            'username' => $username,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ];

        // Tambahkan data nama_hrd dan jabatan jika role adalah company
        if ($request->role == 'company') {
            if ($request->has('nama_hrd')) {
                $userData['nama_hrd'] = $request->nama_hrd;
            }
            if ($request->has('jabatan')) {
                $userData['jabatan'] = $request->jabatan;
            }
        }

        $user = User::create($userData);

        try {
            $admins = Admin::all();
            foreach ($admins as $admin) {
                Notification::send($admin, new NewUserRegisteredNotification($user, $admin));
            }
        } catch (\Throwable $th) {}

        // jika email sudah dikonfigurasi, kirim notifikasi ke pelamar dan perusahaan
        if (checkMailConfig()) {
            if ($user->role == "candidate") {
                $candidate_account_auto_activation_enabled = Setting::where("candidate_account_auto_activation", 1)->count();

                if ($candidate_account_auto_activation_enabled) {
                    Notification::route('mail', $user->email)->notify(new CandidateCreateNotification($user, $request->password));
                } else {
                    Notification::route('mail', $user->email)->notify(new CandidateCreateApprovalPendingNotification($user, $request->password));
                }
            } elseif ($user->role == "company") {
                $employer_auto_activation_enabled = Setting::where("employer_auto_activation", 1)->count();

                if ($employer_auto_activation_enabled) {
                    Notification::route('mail', $user->email)->notify(new CompanyCreatedNotification($user, $request->password));
                } else {
                    Notification::route("mail", $user->email)->notify(new CompanyCreateApprovalPendingNotification($user, $request->password));
                }
            }
        }

        if ($user) {
            return $this->respondWithSuccess([
                'data' => $user,
                'message' => 'Registrasi Berhasil!'
            ]);
        }

        return $this->respondError('Registration Failed');
    }

    /**
     * Retrieve authenticated user's information.
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * If the user is authenticated via Sanctum, returns a JSON response with the user's data.
     * If not authenticated, returns an unauthenticated response.
     */
    public function profile()
    {
        $user = Auth::user();

        return $this->respondWithSuccess([
            'data' => $user
        ]);
    }

    public function sendResetCodeEmail(Request $request)
    {
        $this->validate($request, [
            'email' => 'required|string|email|exists:users,email',
        ]);

        $customer = User::where('email', $request->email)->first();
        $code = rand(100000, 999999);

        $customer->verificationCodes()->create([
            'code' => $code,
            'type' => 'reset_password'
        ]);

        if (checkMailConfig()) {
            $customer->notify(new ResetPassword($code));
        }

        return $this->respondWithSuccess([
            'data' => [
                'message' => 'Kami sudah mengirimkan Anda email kode reset password.',

                // testing only should remove in production
                'code' => $code
            ]
        ]);
    }

    /**
     * Resets the user's password.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * The request must contain the following data:
     * - code: the verification code sent to the user's email
     * - email: the user's email address
     * - password: the new password
     *
     * If the verification code is invalid, it will return an error response.
     * If the verification code has expired, it will return an error response.
     * If the user is not found, it will return a not found response.
     * If the password is successfully reset, it will return a success response.
     */
    public function reset(Request $request)
    {
        $this->validate($request, [
            'code' => 'required',
            'email' => "required|string|max:100|email|exists:users,email",
            'password' => "required|min:8|max:50",
        ]);

        $customer = User::where('email', $request->email)->first();
        $verificationCode = VerificationCode::reset()
                ->where('user_id', $customer->id)
                ->where('code', $request->code)
                ->first();

        if (!$verificationCode) {
            return $this->respondError('Invalid code');
        }elseif ($verificationCode && now()->isAfter($verificationCode->expire_at)) {
            return $this->respondError('Code expired');
        }

        if ($customer) {
            $customer->update([
                'password' => bcrypt($request->password),
            ]);

            return $this->respondWithSuccess([
                'data' => [
                    'message' => 'Password reset successfully'
                ]
            ]);
        }

        return $this->respondNotFound('Invalid code');
    }

    /**
     * Handles social login (Firebase Auth) and returns a JSON response.
     *
     * The request must contain the following data:
     * - Firebasetoken: the Firebase credential's token
     * - role: the user's role (candidate or company)
     * - name: the user's name
     * - email: the user's email address
     *
     * If the verification token is invalid, it will return an error response.
     * If the user is not found, it will return a not found response.
     * If the user is successfully logged in, it will return a success response with the user's data.
     */
    public function socialLogin(Request $request) {

        // Launch Firebase Auth
        $auth = app('firebase.auth');

        // Retrieve the Firebase credential's token
        $idTokenString = $request->input('Firebasetoken');

        try {
            // Try to verify the Firebase credential token with Google
            $verifiedIdToken = $auth->verifyIdToken($idTokenString);
        } catch (\InvalidArgumentException $e) {
            // If the token has the wrong format
            return response()->json([
                'message' => 'Unauthorized - Can\'t parse the token: ' . $e->getMessage()
            ], 401);
        } catch (InvalidToken $e) {
            // If the token is invalid (expired ...)
            return response()->json([
                'message' => 'Unauthorized - Token is invalid: ' . $e->getMessage()
            ], 401);
        }

        // Retrieve the UID (User ID) from the verified Firebase credential's token
        $uid = $verifiedIdToken->getClaim('sub');

        // Retrieve the user model linked with the Firebase UID
        $user = User::where('firebase_uid', $uid)->first();

        // If the user doesn't exist, create a new user (you may customize this)
        try {
            if (!$user) {
                $userData = [
                    'firebase_uid' => $uid,
                    'role' => $request->input('role'),
                    'name' => $request->input('name'),
                    'email' => $request->input('email'),
                ];

                // Tambahkan data nama_hrd dan jabatan jika role adalah company
                if ($request->input('role') == 'company') {
                    if ($request->has('nama_hrd')) {
                        $userData['nama_hrd'] = $request->input('nama_hrd');
                    }
                    if ($request->has('jabatan')) {
                        $userData['jabatan'] = $request->input('jabatan');
                    }
                }

                $user = User::create($userData);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating user: ' . $e->getMessage()
            ], 500);
        }

        // Create a Personal Access Token using Sanctum
        $token =  $user->createToken('job-pilot')->plainTextToken;

        return $this->respondWithSuccess([
            'data' => [
                'token' => $token,
                'message' => 'User data retrieved successfully',
                'user' => $user->role == 'candidate' ? new CandidateResource($user->candidate) : new CompanyResource($user->company)
            ]
        ]);


    }
}
