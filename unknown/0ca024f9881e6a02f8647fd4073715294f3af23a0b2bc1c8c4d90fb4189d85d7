<?php

namespace App\Services\Website\Job;

use App\Http\Traits\JobAble;
use App\Models\Job;

class JobListService
{
    use JobAble;

    /**
     * Get active jobs with eager loading of relationships.
     *
     * @param int $limit Optional limit, set to null to get all jobs
     * @param array $with Relationships to eager load
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLimitedJobs($limit = null, $with = [])
    {
        $defaultRelations = ['company.user', 'category', 'job_type'];
        $relations = !empty($with) ? $with : $defaultRelations;

        $query = Job::with($relations)
            ->select(['id', 'title', 'company_id', 'category_id', 'job_type_id', 'education_id',
                     'min_salary', 'max_salary', 'salary_mode', 'custom_salary', 'deadline',
                     'locality', 'country', 'region', 'district', 'disability_friendly', 'status',
                     'slug', 'is_remote', 'waiting_for_edit_approval', 'created_at', 'featured'])
            ->where('status', 'active')
            ->latest();

        if ($limit) {
            $query->take($limit);
        }

        return $query->get();
    }

    /**
     * Get jobs based on request parameters.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function jobs($request): mixed
    {
        // Get jobs with pagination for display
        $data = $this->getJobs($request);

        // Generate cache key based on request parameters
        $cacheKey = 'jobs_' . md5(json_encode($request->all()) . '_page_' . ($request->page ?? 1));

        // Try to get from cache first
        $limited_jobs = cache()->remember($cacheKey, 10, function() use ($request) {
            // Create optimized query for limited_jobs with database filtering
            $query = Job::with(['company.user', 'category', 'job_type'])
                ->select(['id', 'title', 'company_id', 'category_id', 'job_type_id', 'education_id',
                         'min_salary', 'max_salary', 'salary_mode', 'custom_salary', 'deadline',
                         'locality', 'country', 'region', 'district', 'disability_friendly', 'status',
                         'slug', 'is_remote', 'waiting_for_edit_approval', 'created_at', 'featured'])
                ->where('status', 'active')
                ->withoutEdited();

            // Apply database filters if any
            $query = $this->applyDatabaseFilters($query, $request);

            // Get paginated results
            $perPage = $request->per_page ?? 9;
            return $query->latest()->paginate($perPage)->onEachSide(1);
        });

        $data['limited_jobs'] = $limited_jobs;

        // Get featured jobs
        $featured_jobs = cache()->remember('featured_jobs', 10, function() {
            return Job::with(['company.user', 'category', 'job_type'])
                ->select(['id', 'title', 'company_id', 'category_id', 'job_type_id', 'education_id',
                         'min_salary', 'max_salary', 'salary_mode', 'custom_salary', 'deadline',
                         'locality', 'country', 'region', 'district', 'disability_friendly', 'status',
                         'slug', 'is_remote', 'waiting_for_edit_approval', 'created_at', 'featured'])
                ->where('status', 'active')
                ->where('featured', 1)
                ->latest()
                ->take(9)
                ->get();
        });

        $data['featured_jobs'] = $featured_jobs;

        // Set up other job collections for compatibility
        $data['all_jobs'] = $data['limited_jobs'];
        $data['mix_jobs'] = $data['limited_jobs'];

        $data['resumes'] = $this->getResumes();

        return $data;
    }

    /**
     * Apply filters directly to the database query for better performance
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyDatabaseFilters($query, $request)
    {
        // Filter by keyword
        if ($request->has('keyword') && $request->keyword != null) {
            $query->where('title', 'LIKE', "%{$request->keyword}%");
        }

        // Filter by category
        if ($request->has('kategori') && $request->kategori != null && $request->kategori !== '') {
            $category = \App\Models\JobCategory::where('slug', $request->kategori)->first();
            if ($category) {
                $query->where('category_id', $category->id);
                \Log::info("Filtering by category ID: {$category->id}, slug: {$request->kategori}");
            } else {
                \Log::warning("Category with slug '{$request->kategori}' not found");
            }
        } elseif ($request->has('category') && $request->category != null && $request->category !== '') {
            $category = \App\Models\JobCategory::where('slug', $request->category)->first();
            if ($category) {
                $query->where('category_id', $category->id);
                \Log::info("Filtering by category ID: {$category->id}, slug: {$request->category}");
            } else {
                \Log::warning("Category with slug '{$request->category}' not found");
            }
        }

        // Filter by job type
        if ($request->has('jenis_pekerjaan') && $request->jenis_pekerjaan != null) {
            $jobType = \App\Models\JobTypeTranslation::where('name', $request->jenis_pekerjaan)->first();
            if ($jobType) {
                $query->where('job_type_id', $jobType->job_type_id);
            }
        } elseif ($request->has('job_type') && $request->job_type != null) {
            $jobType = \App\Models\JobTypeTranslation::where('name', $request->job_type)->first();
            if ($jobType) {
                $query->where('job_type_id', $jobType->job_type_id);
            }
        }

        // Filter by education
        if ($request->has('pendidikan') && $request->pendidikan != null) {
            $education = \App\Models\EducationTranslation::where('name', $request->pendidikan)->first();
            if ($education) {
                $query->where('education_id', $education->education_id);
            }
        } elseif ($request->has('education') && $request->education != null) {
            $education = \App\Models\EducationTranslation::where('name', $request->education)->first();
            if ($education) {
                $query->where('education_id', $education->education_id);
            }
        }

        // Filter by kabupaten/kota
        if ($request->has('kabupaten_kota') && $request->kabupaten_kota != null) {
            $query->where('district', $request->kabupaten_kota);
        }

        // Filter by locality/kecamatan
        if ($request->has('kecamatan') && $request->kecamatan != null) {
            $query->where('locality', $request->kecamatan);
        } elseif ($request->has('locality') && $request->locality != null) {
            $query->where('locality', $request->locality);
        }

        // Filter by disability friendly
        if ($request->has('ramah_disabilitas') || $request->has('disability_friendly')) {
            $query->where('disability_friendly', 1);
        }

        // Filter by salary
        if (($request->has('gaji_min') && $request->gaji_min != null) ||
            ($request->has('price_min') && $request->price_min != null)) {
            $minSalary = $request->gaji_min ?? $request->price_min;
            $query->where('min_salary', '>=', $minSalary);
        }

        if (($request->has('gaji_max') && $request->gaji_max != null) ||
            ($request->has('price_max') && $request->price_max != null)) {
            $maxSalary = $request->gaji_max ?? $request->price_max;
            $query->where('max_salary', '<=', $maxSalary);
        }

        // Filter by location
        if ($request->has('location') && $request->location != null) {
            $location = $request->location;
            $query->where(function($q) use ($location) {
                $q->where('country', 'LIKE', "%{$location}%")
                  ->orWhere('address', 'LIKE', "%{$location}%");
            });
        }

        // Filter by remote work
        if ($request->has('is_remote') && $request->is_remote != null) {
            $query->where('is_remote', 1);
        }

        // Filter by gender
        if ($request->has('gender') && $request->gender != null) {
            if ($request->gender == 'male') {
                $query->where(function($q) {
                    $q->where('gender', 'male')
                      ->orWhere('gender', 'both');
                });
            } elseif ($request->gender == 'female') {
                $query->where(function($q) {
                    $q->where('gender', 'female')
                      ->orWhere('gender', 'both');
                });
            }
        }

        return $query;
    }



    /**
     * Get more jobs from database or API.
     *
     * This function will be called when user click the "Load More" button.
     * It will load more jobs from database or API based on the request parameters.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    public function loadMore($request)
    {
        $query = $this->filterJobs($request);
        $query->where('id', '<', $request->id);

        $jobs = $query->take(18)->latest()->get();

        return $jobs;
    }

    /**
     * Get jobs based on category slug.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $slug
     * @return array
     */
    public function categoryJobs($request, string $slug): mixed
    {
        \Log::info("categoryJobs dipanggil dengan slug: {$slug}");

        $data = $this->getJobsCategory($request, $slug);

        // Log data yang diterima dari getJobsCategory
        \Log::info("Data dari getJobsCategory: jobs count = " . ($data['jobs'] ? $data['jobs']->count() : 'null'));

        // Pastikan all_jobs tidak kosong dengan menggunakan jobs
        if (isset($data['jobs']) && $data['jobs']->count() > 0) {
            $data['all_jobs'] = $data['jobs'];
            \Log::info("all_jobs diisi dengan jobs, count: " . $data['all_jobs']->count());
        } else {
            // Jika jobs kosong, coba ambil semua job aktif dengan kategori yang sama
            $category = \App\Models\JobCategory::where('slug', $slug)->first();
            if ($category) {
                $data['all_jobs'] = \App\Models\Job::where('category_id', $category->id)
                    ->where('status', 'active')
                    ->latest()
                    ->get();
                \Log::info("all_jobs diisi dengan query langsung, count: " . $data['all_jobs']->count());
            } else {
                $data['all_jobs'] = collect([]);
                \Log::info("all_jobs diisi dengan collection kosong");
            }
        }

        $data['resumes'] = $this->getResumes();

        return $data;
    }



    /**
     * Retrieve resumes for the authenticated candidate user.
     *
     * @return mixed An array of resumes if the user is authenticated and has a candidate role,
     *               otherwise an empty array.
     */
    protected function getResumes(): mixed
    {
        if (auth('user')->check() && authUser()->role == 'candidate') {
            $resumes = currentCandidate()->resumes;
        } else {
            $resumes = [];
        }

        return $resumes;
    }

    /**
     * Get filter data for job filtering components
     *
     * @return array
     */
    public function getFilterData(): array
    {
        // Cache frequently used data
        $countries = cache()->remember('countries_list', 60*24, function() {
            return \Modules\Location\Entities\Country::all(['id', 'name', 'slug']);
        });

        // Hapus cache untuk kategori agar selalu mendapatkan data terbaru
        cache()->forget('job_categories_list');

        $categories = cache()->remember('job_categories_list', 60*5, function() {
            // Ambil kategori dengan menghitung jumlah lowongan aktif
            return \App\Models\JobCategory::with('translations')
                ->withCount(['jobs' => function($query) {
                    $query->where('status', 'active')
                        ->where('deadline', '>=', now()->toDateString());
                }])
                ->get(['id', 'slug'])
                // Filter hanya kategori yang memiliki lowongan aktif
                ->filter(function($category) {
                    return $category->jobs_count > 0;
                })
                ->sortBy(function($category) {
                    return $category->name; // Ini akan mengakses nama melalui translasi
                });
        });

        $job_roles = cache()->remember('job_roles_list', 60*24, function() {
            return \App\Models\JobRole::with('translations')->get(['id'])->sortBy(function($role) {
                return $role->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $job_types = cache()->remember('job_types_list', 60*24, function() {
            return \App\Models\JobType::with('translations')->get(['id'])->sortBy(function($type) {
                return $type->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $experiences = cache()->remember('experiences_list', 60*24, function() {
            return \App\Models\Experience::with('translations')->get(['id'])->sortBy(function($experience) {
                return $experience->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $educations = cache()->remember('educations_list', 60*24, function() {
            return \App\Models\Education::with('translations')->get(['id'])->sortBy(function($education) {
                return $education->name; // Ini akan mengakses nama melalui translasi
            });
        });

        // Get salary stats
        $salary_stats = cache()->remember('salary_stats', 60*24, function() {
            return [
                'max_salary' => \DB::table('jobs')->max('max_salary'),
                'min_salary' => \DB::table('jobs')->min('min_salary')
            ];
        });

        return [
            'countries' => $countries,
            'categories' => $categories,
            'job_roles' => $job_roles,
            'max_salary' => $salary_stats['max_salary'],
            'min_salary' => $salary_stats['min_salary'],
            'experiences' => $experiences,
            'educations' => $educations,
            'job_types' => $job_types,
            'skills' => cache()->remember('skills_list', 60*24, function() {
                return \App\Models\Skill::with('translations')->get(['id'])->sortBy(function($skill) {
                    return $skill->name; // Ini akan mengakses nama melalui translasi
                });
            }),
            'popularTags' => cache()->remember('popular_tags', 60*24, function() {
                return \App\Models\Tag::popular()
                    ->withCount('tags')
                    ->latest('tags_count')
                    ->get()
                    ->take(10);
            }),
        ];
    }


}
