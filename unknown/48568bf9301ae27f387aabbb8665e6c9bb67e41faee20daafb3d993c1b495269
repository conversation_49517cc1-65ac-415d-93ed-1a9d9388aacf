<?php

namespace App\Http\Livewire\Message;

use App\Models\Message;
use App\Models\MessageThread;
use Carbon\Carbon;
use Livewire\Component;

class MessageDetail extends Component
{
    public $selectedThread = null;
    public $thread = null;
    public $messages = [];
    public $initialThreadId = null;

    protected $listeners = [
        'threadSelected' => 'loadThread',
        'messageSent' => 'loadMessages',
        'ticketStatusUpdated' => 'loadThread',
        'ticketPriorityUpdated' => 'loadThread',
        'ticketCategoryUpdated' => 'loadThread',
    ];

    public function mount($initialThreadId = null)
    {
        $this->initialThreadId = $initialThreadId;

        if ($this->initialThreadId) {
            $this->loadThread($this->initialThreadId);
        }
    }

    public function loadThread($threadId)
    {
        try {
            $this->selectedThread = $threadId;
            $thread = MessageThread::with(['company.user', 'candidate.user', 'job'])->find($threadId);

            if (!$thread) {
                \Log::warning('Thread not found: ' . $threadId);
                $this->dispatchBrowserEvent('hideMessageLoading');
                return;
            }

            $this->thread = $thread;
            $this->loadMessages();
        } catch (\Exception $e) {
            \Log::error('Error in loadThread: ' . $e->getMessage());
            $this->dispatchBrowserEvent('hideMessageLoading');
            return;
        }

        // Tandai pesan sebagai telah dibaca
        if (isset($thread) && $thread) {
            $thread->messages()
                ->where('receiver_id', auth()->id())
                ->where('read', false)
                ->update(['read' => true]);
        }

        // Update URL with thread ID
        $this->dispatchBrowserEvent('updateUrl', ['threadId' => $threadId]);

        // Hide loading spinner
        $this->dispatchBrowserEvent('hideMessageLoading');
    }

    public function loadMessages()
    {
        if (!$this->selectedThread) {
            return;
        }

        try {
            $this->messages = Message::where('message_thread_id', $this->selectedThread)
                ->with([
                    'sender.company',
                    'sender.candidate',
                    'receiver.company',
                    'receiver.candidate'
                ])
                ->orderBy('created_at')
                ->get();
        } catch (\Exception $e) {
            \Log::error('Error in loadMessages: ' . $e->getMessage());
            $this->messages = collect([]);
        }
    }

    public function getGroupedMessagesProperty()
    {
        $groupedMessages = [];

        if (!$this->messages) {
            return $groupedMessages;
        }

        try {
            foreach ($this->messages as $message) {
                $date = $message->created_at->format('Y-m-d');
                if (!isset($groupedMessages[$date])) {
                    $groupedMessages[$date] = [];
                }
                $groupedMessages[$date][] = $message;
            }
        } catch (\Exception $e) {
            \Log::error('Error in getGroupedMessagesProperty: ' . $e->getMessage());
        }

        return $groupedMessages;
    }

    public function formatDate($date)
    {
        $carbonDate = Carbon::parse($date);
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();

        if ($carbonDate->isSameDay($today)) {
            return 'Hari Ini';
        } elseif ($carbonDate->isSameDay($yesterday)) {
            return 'Kemarin';
        } else {
            return $carbonDate->isoFormat('dddd, D MMMM YYYY');
        }
    }

    /**
     * Update ticket status
     */
    public function updateTicketStatus($status)
    {
        if (!$this->thread || !in_array($status, ['open', 'pending', 'closed'])) {
            return;
        }

        try {
            // Jika status sama, tidak perlu update
            if ($this->thread->status === $status) {
                return;
            }

            $oldStatus = $this->thread->status;
            $this->thread->status = $status;

            // Jika status closed, set closed_at dan closed_by
            if ($status === 'closed') {
                $this->thread->closed_at = now();
                $this->thread->closed_by = auth()->id();

                // Tambahkan pesan sistem bahwa tiket telah ditutup
                Message::create([
                    'message_thread_id' => $this->thread->id,
                    'sender_id' => auth()->id(),
                    'receiver_id' => null,
                    'body' => 'Tiket ditutup oleh ' . auth()->user()->name,
                    'type' => 'informasi',
                    'can_reply' => false,
                    'read' => true,
                ]);
            } elseif ($oldStatus === 'closed' && in_array($status, ['open', 'pending'])) {
                // Jika tiket dibuka kembali, reset closed_at dan closed_by
                $this->thread->closed_at = null;
                $this->thread->closed_by = null;

                // Tambahkan pesan sistem bahwa tiket telah dibuka kembali
                Message::create([
                    'message_thread_id' => $this->thread->id,
                    'sender_id' => auth()->id(),
                    'receiver_id' => null,
                    'body' => 'Tiket dibuka kembali oleh ' . auth()->user()->name,
                    'type' => 'informasi',
                    'can_reply' => true,
                    'read' => true,
                ]);
            }

            $this->thread->save();

            // Reload messages
            $this->loadMessages();

            // Emit event
            $this->emit('ticketStatusUpdated', $this->thread->id);

            // Show notification
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'success',
                'message' => 'Status tiket berhasil diperbarui menjadi ' . ucfirst($status)
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in updateTicketStatus: ' . $e->getMessage());

            // Show error notification
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'error',
                'message' => 'Gagal memperbarui status tiket: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update ticket priority
     */
    public function updateTicketPriority($priority)
    {
        if (!$this->thread || !in_array($priority, ['low', 'medium', 'high', 'urgent'])) {
            return;
        }

        // Hanya admin yang dapat mengubah prioritas
        if (auth()->user()->role !== 'admin') {
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'error',
                'message' => 'Hanya admin yang dapat mengubah prioritas tiket'
            ]);
            return;
        }

        try {
            // Jika prioritas sama, tidak perlu update
            if ($this->thread->priority === $priority) {
                return;
            }

            $oldPriority = $this->thread->priority;
            $this->thread->priority = $priority;
            $this->thread->save();

            // Tambahkan pesan sistem bahwa prioritas tiket telah diubah
            Message::create([
                'message_thread_id' => $this->thread->id,
                'sender_id' => auth()->id(),
                'receiver_id' => null,
                'body' => 'Prioritas tiket diubah dari ' . ucfirst($oldPriority) . ' menjadi ' . ucfirst($priority) . ' oleh ' . auth()->user()->name,
                'type' => 'informasi',
                'can_reply' => false,
                'read' => true,
            ]);

            // Reload messages
            $this->loadMessages();

            // Emit event
            $this->emit('ticketPriorityUpdated', $this->thread->id);

            // Show notification
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'success',
                'message' => 'Prioritas tiket berhasil diperbarui menjadi ' . ucfirst($priority)
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in updateTicketPriority: ' . $e->getMessage());

            // Show error notification
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'error',
                'message' => 'Gagal memperbarui prioritas tiket: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update ticket category
     */
    public function updateTicketCategory($category)
    {
        if (!$this->thread || !in_array($category, ['general', 'technical', 'billing', 'other'])) {
            return;
        }

        // Hanya admin yang dapat mengubah kategori
        if (auth()->user()->role !== 'admin') {
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'error',
                'message' => 'Hanya admin yang dapat mengubah kategori tiket'
            ]);
            return;
        }

        try {
            // Jika kategori sama, tidak perlu update
            if ($this->thread->category === $category) {
                return;
            }

            $oldCategory = $this->thread->category;
            $this->thread->category = $category;
            $this->thread->save();

            // Tambahkan pesan sistem bahwa kategori tiket telah diubah
            Message::create([
                'message_thread_id' => $this->thread->id,
                'sender_id' => auth()->id(),
                'receiver_id' => null,
                'body' => 'Kategori tiket diubah dari ' . ucfirst($oldCategory) . ' menjadi ' . ucfirst($category) . ' oleh ' . auth()->user()->name,
                'type' => 'informasi',
                'can_reply' => false,
                'read' => true,
            ]);

            // Reload messages
            $this->loadMessages();

            // Emit event
            $this->emit('ticketCategoryUpdated', $this->thread->id);

            // Show notification
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'success',
                'message' => 'Kategori tiket berhasil diperbarui menjadi ' . ucfirst($category)
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in updateTicketCategory: ' . $e->getMessage());

            // Show error notification
            $this->dispatchBrowserEvent('showToast', [
                'type' => 'error',
                'message' => 'Gagal memperbarui kategori tiket: ' . $e->getMessage()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.message.message-detail');
    }
}
