<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\Company;
use App\Models\Feedback;
use App\Models\User;
use App\Notifications\FeedbackReadNotification;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class FeedbackController extends Controller
{
    /**
     * Display a listing of the feedback.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $feedbackId = $request->feedback_id;
        return view('backend.feedback.index', compact('feedbackId'));
    }

    /**
     * Get feedback data for DataTables.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(Request $request)
    {
        // Return summary data if requested
        if ($request->has('summary')) {
            $total = Feedback::count();
            $candidate = Feedback::where('user_type', 'candidate')->count();
            $company = Feedback::where('user_type', 'company')->count();
            $guest = Feedback::where('user_type', 'guest')->count();
            $read = Feedback::where('is_read', true)->count();
            $unread = Feedback::where('is_read', false)->count();

            // Calculate average rating
            $avgRating = 0;
            if ($total > 0) {
                $avgRating = number_format(Feedback::avg('rating'), 1);
            }

            return response()->json([
                'summary' => [
                    'total' => $total,
                    'candidate' => $candidate,
                    'company' => $company,
                    'guest' => $guest,
                    'read' => $read,
                    'unread' => $unread,
                    'average_rating' => $avgRating
                ]
            ]);
        }

        $feedback = Feedback::query();

        // Filter by feedback ID if provided
        if ($request->has('feedback_id') && $request->feedback_id) {
            $feedback->where('id', $request->feedback_id);
        }

        // Filter by user type if provided
        if ($request->has('user_type') && $request->user_type) {
            $feedback->where('user_type', $request->user_type);
        }

        // Filter by read status if provided
        if ($request->has('status')) {
            if ($request->status === 'read') {
                $feedback->where('is_read', true);
            } elseif ($request->status === 'unread') {
                $feedback->where('is_read', false);
            }
        }

        // Order by created_at desc (newest first)
        $feedback->orderBy('created_at', 'desc');

        return DataTables::of($feedback)
            ->addIndexColumn()
            ->addColumn('user_info', function ($row) {
                $type = $this->getUserTypeLabel($row->user_type);
                return '<span class="badge bg-' . $this->getUserTypeBadgeColor($row->user_type) . '">' . $type . '</span>';
            })
            ->addColumn('rating_stars', function ($row) {
                return $row->rating_stars;
            })
            ->addColumn('created_date', function ($row) {
                return $row->created_at->format('d M Y H:i');
            })
            ->addColumn('action', function ($row) {
                $buttons = [];

                // View button
                $viewBtn = '<a href="javascript:void(0)" data-id="' . $row->id . '" class="btn btn-sm btn-info view-feedback-btn" data-toggle="tooltip" title="Lihat Detail">
                    <i class="fas fa-eye"></i>
                </a>';
                $buttons[] = $viewBtn;

                // Mark as read button
                if (!$row->is_read) {
                    $markBtn = '<a href="javascript:void(0)" data-id="' . $row->id . '" class="btn btn-sm btn-success mark-as-read-btn" data-toggle="tooltip" title="Tandai Dibaca">
                        <i class="fas fa-check-double"></i>
                    </a>';
                    $buttons[] = $markBtn;
                }

                // Profile button for company or candidate
                if ($row->user_id && ($row->user_type == 'company' || $row->user_type == 'candidate')) {
                    $profileBtn = '<a href="javascript:void(0)" data-id="' . $row->user_id . '" data-type="' . $row->user_type . '" class="btn btn-sm btn-primary view-profile-btn" data-toggle="tooltip" title="Lihat Profil">
                        <i class="fas fa-user"></i>
                    </a>';
                    $buttons[] = $profileBtn;
                }

                // Delete button
                $deleteBtn = '<a href="javascript:void(0)" data-id="' . $row->id . '" class="btn btn-sm btn-danger delete-feedback-btn" data-toggle="tooltip" title="Hapus">
                    <i class="fas fa-trash"></i>
                </a>';
                $buttons[] = $deleteBtn;

                return '<div class="btn-group" role="group">' . implode(' ', $buttons) . '</div>';
            })
            ->rawColumns(['user_info', 'rating_stars', 'action'])
            ->make(true);
    }

    /**
     * Get feedback details.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $feedback = Feedback::findOrFail($id);

        // Mark as read if not already
        if (!$feedback->is_read) {
            $feedback->update(['is_read' => true]);
        }

        return response()->json([
            'success' => true,
            'data' => $feedback
        ]);
    }

    /**
     * Mark feedback as read.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead($id)
    {
        $feedback = Feedback::findOrFail($id);

        // Jika sudah dibaca, tidak perlu melakukan apa-apa
        if ($feedback->is_read) {
            return response()->json([
                'success' => true,
                'message' => 'Saran sudah ditandai sebagai dibaca sebelumnya'
            ]);
        }

        // Update status dibaca
        $feedback->update(['is_read' => true]);

        // Kirim notifikasi ke pengguna jika ada user_id
        if ($feedback->user_id) {
            $user = null;

            // Cari user berdasarkan tipe
            if ($feedback->user_type === 'company') {
                $user = Company::find($feedback->user_id);
            } elseif ($feedback->user_type === 'candidate') {
                $user = Candidate::find($feedback->user_id);
            }

            // Kirim notifikasi jika user ditemukan
            if ($user) {
                $user->notify(new FeedbackReadNotification($feedback));
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Saran berhasil ditandai sebagai dibaca'
        ]);
    }

    /**
     * Remove the specified feedback from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $feedback = Feedback::findOrFail($id);
        $feedback->delete();

        return response()->json([
            'success' => true,
            'message' => 'Saran berhasil dihapus'
        ]);
    }

    /**
     * Get badge color based on user type.
     *
     * @param  string  $userType
     * @return string
     */
    /**
     * Get user type label.
     *
     * @param  string  $userType
     * @return string
     */
    private function getUserTypeLabel($userType)
    {
        switch ($userType) {
            case 'candidate':
                return 'Pencaker';
            case 'company':
                return 'Perusahaan';
            default:
                return 'Tamu';
        }
    }

    /**
     * Get badge color based on user type.
     *
     * @param  string  $userType
     * @return string
     */
    /**
     * Get user profile information.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserProfile(Request $request)
    {
        $userId = $request->user_id;
        $userType = $request->user_type;

        if (!$userId || !$userType) {
            return response()->json([
                'success' => false,
                'message' => 'Data pengguna tidak lengkap'
            ]);
        }

        $user = null;
        $profileData = null;

        if ($userType === 'company') {
            $user = Company::with(['user', 'industry', 'organization_type'])->find($userId);
            if ($user) {
                $profileData = [
                    'name' => $user->user->name ?? 'Tidak ada nama',
                    'email' => $user->user->email ?? 'Tidak ada email',
                    'logo' => $user->logo ? asset($user->logo) : asset('backend/image/default.png'),
                    'bio' => $user->bio ?? 'Tidak ada bio',
                    'website' => $user->website ?? '-',
                    'industry' => $user->industry ? $user->industry->name : '-',
                    'organization_type' => $user->organization_type ? $user->organization_type->name : '-',
                    'establishment_date' => $user->establishment_date ? $user->establishment_date->format('d F Y') : '-',
                    'team_size' => $user->team_size ?? '-',
                    'address' => $user->full_address ?? '-',
                    'profile_link' => route('website.employe.details', $user->id)
                ];
            }
        } elseif ($userType === 'candidate') {
            $user = Candidate::with(['user', 'profession', 'experience', 'education'])->find($userId);
            if ($user) {
                $profileData = [
                    'name' => $user->user->name ?? 'Tidak ada nama',
                    'email' => $user->user->email ?? 'Tidak ada email',
                    'photo' => $user->photo ? asset($user->photo) : asset('backend/image/default.png'),
                    'profession' => $user->profession ? $user->profession->name : '-',
                    'bio' => $user->bio ?? 'Tidak ada bio',
                    'birth_date' => $user->birth_date ? $user->birth_date->format('d F Y') : '-',
                    'gender' => $user->gender ?? '-',
                    'experience' => $user->experience ? $user->experience->name : '-',
                    'education' => $user->education ? $user->education->name : '-',
                    'address' => $user->full_address ?? '-',
                    'profile_link' => route('website.candidate.details', $user->id)
                ];
            }
        }

        if (!$profileData) {
            return response()->json([
                'success' => false,
                'message' => 'Profil pengguna tidak ditemukan'
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $profileData,
            'type' => $userType
        ]);
    }

    /**
     * Get badge color based on user type.
     *
     * @param  string  $userType
     * @return string
     */
    private function getUserTypeBadgeColor($userType)
    {
        switch ($userType) {
            case 'candidate':
                return 'primary';
            case 'company':
                return 'success';
            default:
                return 'info';
        }
    }
}
