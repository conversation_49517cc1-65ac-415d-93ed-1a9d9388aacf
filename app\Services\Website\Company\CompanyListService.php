<?php

namespace App\Services\Website\Company;

use App\Models\Company;
use App\Models\IndustryType;
use App\Models\OrganizationType;
use App\Models\TeamSize;
use Modules\Location\Entities\Country;

class CompanyListService
{
    /**
     * Get company list
     */
    public function execute($request): array
    {
        $query = Company::with('user', 'user.contactInfo', 'industry.translations')
            ->withCount([
                'jobs as activejobs' => function ($q) {
                    $q->where('status', 'active');

                    $selected_country = session()->get('selected_country');
                    if ($selected_country && $selected_country != null && $selected_country != 'all') {
                        $country = selected_country()->name;
                        $q->where('country', 'LIKE', "%$country%");
                    } else {
                        $setting = loadSetting();
                        if ($setting->app_country_type == 'single_base') {
                            if ($setting->app_country) {
                                $country = Country::where('id', $setting->app_country)->first();
                                if ($country) {
                                    $q->where('country', 'LIKE', "%$country->name%");
                                }
                            }
                        }
                    }
                },
            ])
            ->withCount([
                'bookmarkCandidateCompany as candidatemarked' => function ($q) {
                    $q->where('user_id', auth()->id());
                },
            ])
            ->withCasts(['candidatemarked' => 'boolean'])
            ->active();

        // Keyword search
        if ($request->has('keyword') && $request->keyword != null) {
            session(['header_search_role' => 'company']);

            $keyword = $request->keyword;
            $query->whereHas('user', function ($q) use ($keyword) {
                $q->where('name', 'LIKE', "%$keyword%");
            });
        }

        // location search
        if ($request->has('lat') && $request->has('long') && $request->lat != null && $request->long != null) {
            $location = $request->location ? $request->location : '';
            $query->where('country', 'LIKE', "%$location%");
        }

        // Filter by lokasi (district)
        if ($request->has('lokasi') && $request->lokasi != null) {
            $query->where('district', $request->lokasi);
        }

        // Industry Type - Single value (for backward compatibility)
        if ($request->has('industry_type') && !is_array($request->industry_type) && $request->industry_type !== null && $request->industry_type !== '') {
            $query->where('industry_type_id', $request->industry_type);
        }

        // Industry Type - Multiple values
        if ($request->has('industry_type') && is_array($request->industry_type) && count($request->industry_type) > 0) {
            $query->whereIn('industry_type_id', $request->industry_type);
        }

        // Organization Type - Single value
        if ($request->has('organization_type') && !is_array($request->organization_type) && $request->organization_type !== null && $request->organization_type !== '') {
            $query->where('organization_type_id', $request->organization_type);
        }

        // Organization Type - Multiple values (for backward compatibility)
        if ($request->has('organization_type') && is_array($request->organization_type) && count($request->organization_type) > 0) {
            $query->whereIn('organization_type_id', $request->organization_type);
        }

        // Team Size
        if ($request->has('team_size') && $request->team_size !== null) {
            $team_size = TeamSize::where('name', $request->team_size)->first();
            $query->where('team_size_id', $team_size->id);
        }

        // Sebelumnya
        $companies = $query->latest('activejobs')->paginate(12);
        // tapi error missing parameter

        // setelah diganti ini oke, tapi tampilannya tidak sama:
        // $companies = $query->whereHas('user', function ($q) {
        //     $q->whereNotNull('username');
        // })->latest('activejobs')->paginate(12);

        $team_sizes = TeamSize::all(['id']);
        $industries = IndustryType::all();
        $organization_types = OrganizationType::all();

        return [
            'companies' => $companies,
            'industries' => $industries,
            'organization_types' => $organization_types,
            'teamsizes' => $team_sizes,
        ];
    }
}
