<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AppliedJob;
use App\Models\CandidateResume;
use App\Models\Job;
use Carbon\Carbon;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\Request;

class CompanyJobsController extends Controller
{
    use ApiResponseHelpers;

    // untuk mendapatkan semua loker untuk perusahaan yang diautentikasi
    public function getJobs()
    {
        $jobs = Job::where('company_id', auth('sanctum')->user()->company->id)
        ->select(['id', 'title', 'company_id', 'country', 'max_salary', 'min_salary', 'job_type_id', 'slug', 'deadline', 'status'])
        // ->whereDate('deadline', '>', Carbon::now()->toDateString())
        ->with('company:id', 'job_type:id,name')->withCount('appliedJobs')
        // ->where('status', request('status'))
        ->when($status = request('status'), function($query) use($status){
            $query->where('status', $status);
        }, function($query) use($status){
            $query->where('status', 'active');
        })
        ->latest()->paginate(5)->withQueryString();

        return $this->respondWithSuccess([
            'data' => $jobs,
        ]);
    }

    // Ambil semua lamaran pekerjaan
    public function applications($id)
    {
        $application_groups = auth('sanctum')->user()
            ->company
            ->applicationGroups()
            ->with(['applications' => function ($query) use ($id) {
                $query->where('job_id', $id)->with(['apiCandidate' => function ($query) {
                    return $query->select('id', 'user_id', 'profession_id', 'experience_id', 'education_id')

                        ->with('profession', 'education:id,name', 'experience:id,name', 'user:id,name,username,image');
                }]);
            }])
            ->get();

        return $this->respondWithSuccess([
            'data' => $application_groups,
        ]);
    }

    /**
     * Update application group for applied job
     *
     * @param int $id applied job id
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function applicationGroupUpdate($id, Request $request)
    {
        $appliedJob = AppliedJob::find($id);
        $applicationGroup = ApplicationGroup::find($request->group);

        // Find corresponding default application group
        $defaultGroup = \App\Models\DefaultApplicationGroup::where('name', $applicationGroup->name)->first();

        $appliedJob->update([
            'application_group_id' => $request->group,
            'default_application_group_id' => $defaultGroup ? $defaultGroup->id : null,
        ]);

        return $this->respondWithSuccess([
            'data' => [
                'message' => 'Lamaran berhasil diperbarui!'
            ]
        ]);
    }

    /**
     * Retrieve the download link for a candidate's CV.
     *
     * @param int $id The ID of the candidate resume.
     * @return string The asset URL for the candidate resume file.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException If the resume is not found.
     */
    public function downloadCv($id)
    {
        // CandidateResume $resume
        $resume = CandidateResume::findOrFail($id);
        // $filename = time() . '.pdf';

        // $headers = ['Content-Type: application/pdf',  'filename' => $filename,];
        // $fileName = rand() . '-resume' . '.pdf';

        // return response()->download($resume->file, $fileName, $headers);
        return asset($resume->file);

    }
}
