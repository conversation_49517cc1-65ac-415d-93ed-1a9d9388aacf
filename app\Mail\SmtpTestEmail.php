<?php

namespace App\Mail;

use App\Services\EmailLogService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SmtpTestEmail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = 'Test Konfigurasi SMTP';
        $from = config('mail.from.address');
        $fromName = config('mail.from.name');

        // Log the email
        $view = $this->buildView();
        $body = $view['html'] ?? view('mails.smtp-test-email')->render();

        EmailLogService::log(
            $from,
            $this->to[0]['address'] ?? 'unknown',
            $subject,
            $body
        );

        return $this->subject($subject)
            ->from($from, $fromName)
            ->markdown('mails.smtp-test-email');
    }
}
