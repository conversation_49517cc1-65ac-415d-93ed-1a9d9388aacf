<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Iyzipay extends Model
{
    use HasFactory;
    public static function options()
    {
        $options = new \Iyzipay\Options();
        $options->setApiKey('sandbox-********************************');
        $options->setSecretKey('sandbox-********************************');
        $options->setBaseUrl('https://sandbox-api.iyzipay.com');

        return $options;
    }
}
