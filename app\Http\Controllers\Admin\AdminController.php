<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\Education;
use App\Models\EducationTranslation;
use App\Models\CandidateResume;
use App\Models\Company;
use App\Models\Earning;
use App\Models\Job;
use App\Models\LoginLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Redirect ke dashboard untuk memastikan semua data tersedia
        return redirect()->route('admin.dashboard');
    }

    /*
    * Show the application dashboard.
    *
    * @return \Illuminate\Contracts\Support\Renderable
    */
    public function dashboard()
    {
        session(['layout_mode' => 'left_nav']);

        // Get last login info for welcome modal
        if (auth('admin')->check()) {
            $admin = auth('admin')->user();
            $lastLogin = LoginLog::where('user_id', $admin->id)
                ->where('user_type', 'admin')
                ->where('is_active', false)
                ->orderBy('login_at', 'desc')
                ->first();

            if ($lastLogin) {
                // Simpan informasi login terakhir di session
                session(['last_login' => $lastLogin, 'show_welcome_modal' => true]);
                // Catatan: Pengecekan "jangan tampilkan lagi" dilakukan di JavaScript
            }
        }

        $jobs = Job::withoutEdited()->get();
        $data = [
            'all_jobs' => $jobs->count(),
            'active_jobs' => $jobs->where('status', 'active')->count(),
            'expire_jobs' => $jobs->where('status', 'expired')->count(),
            'pending_jobs' => $jobs->where('status', 'pending')->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'candidates' => Candidate::count(),
            'companies' => Company::count(),
            'earnings' => currencyConversion(Earning::sum('usd_amount')),
            'email_verification' => setting('email_verification'),
        ];

        $pendidikan_pencaker = Candidate::select('education_id', DB::raw('count(*) as total'))
        ->whereNotNull('education_id') // Pastikan education_id tidak null
        ->groupBy('education_id')
        ->orderBy('total', 'desc')
        ->get()
        ->map(function ($item) {
            $item->education_name = Education::find($item->education_id)?->name ?? 'Tidak Diketahui';
            return $item;
        });

        // Data Earnings Per Bulan
        $months = Earning::select(
            DB::raw('MIN(created_at) AS created_at'),
            DB::raw('sum(usd_amount) as `amount`'),
            DB::raw("DATE_FORMAT(created_at,'%M') as month")
        )
            ->where('created_at', '>', Carbon::now()->startOfYear())
            ->groupBy('month')
            ->orderBy('created_at')
            ->get();

        $earnings = $this->formatEarnings($months);

        // Data Lowongan Terbaru
        $latest_jobs = Job::withoutEdited()
            ->with(['company', 'job_type', 'experience'])
            ->latest()
            ->take(5)
            ->get();

        // Ambil tahun-tahun yang ada di data
        $available_years = Job::withoutEdited()
            ->selectRaw('YEAR(created_at) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Jika tidak ada data, gunakan tahun ini
        if (empty($available_years)) {
            $available_years = [Carbon::now()->year];
        }

        // Ambil tahun dari request (default: tahun terbaru)
        $tahun = request('tahun', $available_years[0]);

        // Buat array bulan dalam format singkat Bahasa Indonesia
        $bulanList = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];

        // Ambil data lowongan berdasarkan bulan dalam tahun yang dipilih
        $loker_perbulan = Job::withoutEdited()
            ->select(
                DB::raw("MONTH(created_at) as month_number"), // Ambil angka bulan (1-12)
                DB::raw("COUNT(*) as total")
            )
            ->whereYear('created_at', $tahun) // Hanya ambil tahun yang dipilih
            ->groupBy('month_number') // Kelompokkan berdasarkan angka bulan
            ->orderBy('month_number') // Urutkan berdasarkan angka bulan
            ->get()
            ->pluck('total', 'month_number')
            ->toArray();

        // Ambil data pencaker berdasarkan bulan dalam tahun yang dipilih
        $pencaker_perbulan = User::where('role', 'candidate')
            ->select(
                DB::raw("MONTH(created_at) as month_number"),
                DB::raw("COUNT(*) as total")
            )
            ->whereYear('created_at', $tahun)
            ->groupBy('month_number')
            ->orderBy('month_number')
            ->get()
            ->pluck('total', 'month_number')
            ->toArray();

        // Ambil data perusahaan berdasarkan bulan dalam tahun yang dipilih
        $perusahaan_perbulan = User::where('role', 'company')
            ->select(
                DB::raw("MONTH(created_at) as month_number"),
                DB::raw("COUNT(*) as total")
            )
            ->whereYear('created_at', $tahun)
            ->groupBy('month_number')
            ->orderBy('month_number')
            ->get()
            ->pluck('total', 'month_number')
            ->toArray();

        // Buat array data lengkap Januari - Desember
        $lokerData = [
            'months' => $bulanList,
            'totals' => array_map(fn($bulanIndex) => $loker_perbulan[$bulanIndex] ?? 0, range(1, 12)), // Isi 0 jika tidak ada data
            'pencaker' => array_map(fn($bulanIndex) => $pencaker_perbulan[$bulanIndex] ?? 0, range(1, 12)),
            'perusahaan' => array_map(fn($bulanIndex) => $perusahaan_perbulan[$bulanIndex] ?? 0, range(1, 12)),
            'tahun' => $tahun
        ];

        // Data Earnings Terbaru
        $latest_earnings = Earning::with('plan', 'manualPayment:id,name')
            ->latest()
            ->take(10)
            ->get();

        // Data Pengguna Terbaru
        $users = User::select(['id', 'name', 'email', 'role', 'status', 'email_verified_at', 'created_at', 'image', 'username'])
            ->latest()
            ->take(5)
            ->get();

        // Data Lokasi Pencaker
        $lokasi_pencaker = DB::table('users')
            ->select('kecamatan', DB::raw('count(*) as total'))
            ->orderBy('total', 'desc')
            ->groupBy('kecamatan')
            ->limit(5)
            ->get();

        // Data Pencaker Disabilitas
        $pencaker_disabilitas = Candidate::where('disabilitas', 1)->count();

        // Data Status Lamaran
        $application_status = [
            'accepted' => DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Diterima')
                          ->orWhere('application_groups.name', 'Diterima')
                          ->orWhere('application_groups.name', 'Accepted');
                })
                ->count(),
            'interview' => DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Interview')
                          ->orWhere('application_groups.name', 'Interview');
                })
                ->count(),
            'rejected' => DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Ditolak')
                          ->orWhere('application_groups.name', 'Ditolak')
                          ->orWhere('application_groups.name', 'Tolak')
                          ->orWhere('application_groups.name', 'Rejected');
                })
                ->count(),
            'pending' => DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Semua Lamaran')
                          ->orWhere('application_groups.name', 'Semua Lamaran')
                          ->orWhere('application_groups.name', 'All Applications');
                })
                ->count(),
        ];
        $application_status['total'] = DB::table('applied_jobs')->count();

        // Data Tren Lamaran per Bulan
        $application_trend = [
            'accepted' => [],
            'interview' => [],
            'rejected' => [],
            'pending' => [],
        ];

        // Ambil data lamaran berdasarkan status dan bulan
        foreach (range(1, 12) as $month) {
            $application_trend['accepted'][] = DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->whereMonth('applied_jobs.created_at', $month)
                ->whereYear('applied_jobs.created_at', $tahun)
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Diterima')
                          ->orWhere('application_groups.name', 'Diterima')
                          ->orWhere('application_groups.name', 'Accepted');
                })
                ->count();

            $application_trend['interview'][] = DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->whereMonth('applied_jobs.created_at', $month)
                ->whereYear('applied_jobs.created_at', $tahun)
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Interview')
                          ->orWhere('application_groups.name', 'Interview');
                })
                ->count();

            $application_trend['rejected'][] = DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->whereMonth('applied_jobs.created_at', $month)
                ->whereYear('applied_jobs.created_at', $tahun)
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Ditolak')
                          ->orWhere('application_groups.name', 'Ditolak')
                          ->orWhere('application_groups.name', 'Tolak')
                          ->orWhere('application_groups.name', 'Rejected');
                })
                ->count();

            $application_trend['pending'][] = DB::table('applied_jobs')
                ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                ->whereMonth('applied_jobs.created_at', $month)
                ->whereYear('applied_jobs.created_at', $tahun)
                ->where(function($query) {
                    $query->where('default_application_groups.name', 'Semua Lamaran')
                          ->orWhere('application_groups.name', 'Semua Lamaran')
                          ->orWhere('application_groups.name', 'All Applications');
                })
                ->count();
        }

        // Negara dengan Lowongan Terbanyak
        $popular_countries = DB::table('jobs')
            ->select('country', DB::raw('count(*) as total'))
            ->orderBy('total', 'desc')
            ->groupBy('country')
            ->limit(5)
            ->get();

        // Mata Uang Saat Ini
        $current_currency = currentCurrency();

        $today = Carbon::today();

        // ________________________________
        // |                               |
        // |        Kategori umur          |
        // |_______________________________|

        $umur_ranges = [
            ['min' => 18, 'max' => 25],
            ['min' => 26, 'max' => 35],
            ['min' => 36, 'max' => 45],
            ['min' => 46, 'max' => 55],
            ['min' => 56, 'max' => 100], // Usia 56 tahun ke atas
        ];

        // Query untuk menghitung jumlah pencaker berdasarkan kategori umur
        $umur_pencaker = collect($umur_ranges)->map(function ($range) use ($today) {
            $min_date = $today->copy()->subYears($range['max'])->format('Y-m-d');
            $max_date = $today->copy()->subYears($range['min'])->format('Y-m-d');

            $count = DB::table('users')
                ->whereBetween('tanggal_lahir', [$min_date, $max_date])
                ->count();

            return [
                'range' => "{$range['min']} - {$range['max']}",
                'total' => $count,
            ];
        });

        return view('backend.index', compact(
            'data', 'earnings', 'popular_countries', 'lokasi_pencaker',
            'lokerData', 'latest_jobs', 'latest_earnings', 'users',
            'current_currency', 'pencaker_disabilitas', 'tahun', 'pendidikan_pencaker', 'umur_pencaker',
            'application_status', 'application_trend', 'available_years'
        ));
    }

    /**
     * Export dashboard statistics as PDF
     *
     * @param Request $request
     * @return Response
     */
    public function exportDashboard(Request $request)
    {
        try {
            // Ambil tahun dari request (default: tahun ini)
            $tahun = $request->get('tahun', Carbon::now()->year);

            // Buat array bulan dalam format singkat Bahasa Indonesia
            $bulanList = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];

            // Data Status Lamaran
            $application_status = [
                'accepted' => DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Diterima')
                              ->orWhere('application_groups.name', 'Diterima')
                              ->orWhere('application_groups.name', 'Accepted');
                    })
                    ->count(),
                'interview' => DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Interview')
                              ->orWhere('application_groups.name', 'Interview');
                    })
                    ->count(),
                'rejected' => DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Ditolak')
                              ->orWhere('application_groups.name', 'Ditolak')
                              ->orWhere('application_groups.name', 'Tolak')
                              ->orWhere('application_groups.name', 'Rejected');
                    })
                    ->count(),
                'pending' => DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Semua Lamaran')
                              ->orWhere('application_groups.name', 'Semua Lamaran')
                              ->orWhere('application_groups.name', 'All Applications');
                    })
                    ->count(),
            ];
            $application_status['total'] = DB::table('applied_jobs')->count();

            // Data Tren Lamaran per Bulan
            $application_trend = [
                'accepted' => [],
                'interview' => [],
                'rejected' => [],
                'pending' => [],
            ];

            // Ambil data lamaran berdasarkan status dan bulan
            foreach (range(1, 12) as $month) {
                $application_trend['accepted'][] = DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->whereMonth('applied_jobs.created_at', $month)
                    ->whereYear('applied_jobs.created_at', $tahun)
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Diterima')
                              ->orWhere('application_groups.name', 'Diterima')
                              ->orWhere('application_groups.name', 'Accepted');
                    })
                    ->count();

                $application_trend['interview'][] = DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->whereMonth('applied_jobs.created_at', $month)
                    ->whereYear('applied_jobs.created_at', $tahun)
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Interview')
                              ->orWhere('application_groups.name', 'Interview');
                    })
                    ->count();

                $application_trend['rejected'][] = DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->whereMonth('applied_jobs.created_at', $month)
                    ->whereYear('applied_jobs.created_at', $tahun)
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Ditolak')
                              ->orWhere('application_groups.name', 'Ditolak')
                              ->orWhere('application_groups.name', 'Tolak')
                              ->orWhere('application_groups.name', 'Rejected');
                    })
                    ->count();

                $application_trend['pending'][] = DB::table('applied_jobs')
                    ->leftJoin('default_application_groups', 'applied_jobs.default_application_group_id', '=', 'default_application_groups.id')
                    ->leftJoin('application_groups', 'applied_jobs.application_group_id', '=', 'application_groups.id')
                    ->whereMonth('applied_jobs.created_at', $month)
                    ->whereYear('applied_jobs.created_at', $tahun)
                    ->where(function($query) {
                        $query->where('default_application_groups.name', 'Semua Lamaran')
                              ->orWhere('application_groups.name', 'Semua Lamaran')
                              ->orWhere('application_groups.name', 'All Applications');
                    })
                    ->count();
            }

            // Data Pendidikan Pencaker
            $pendidikan_pencaker = Candidate::select('education_id', DB::raw('count(*) as total'))
                ->whereNotNull('education_id')
                ->groupBy('education_id')
                ->orderBy('total', 'desc')
                ->get()
                ->map(function ($item) {
                    $item->education_name = Education::find($item->education_id)?->name ?? 'Tidak Diketahui';
                    return $item;
                });

            // Data Umur Pencaker
            $today = Carbon::today();
            $umur_ranges = [
                ['min' => 18, 'max' => 25],
                ['min' => 26, 'max' => 35],
                ['min' => 36, 'max' => 45],
                ['min' => 46, 'max' => 55],
                ['min' => 56, 'max' => 100],
            ];

            $umur_pencaker = collect($umur_ranges)->map(function ($range) use ($today) {
                $min_date = $today->copy()->subYears($range['max'])->format('Y-m-d');
                $max_date = $today->copy()->subYears($range['min'])->format('Y-m-d');

                $count = DB::table('users')
                    ->whereBetween('tanggal_lahir', [$min_date, $max_date])
                    ->count();

                return [
                    'range' => "{$range['min']} - {$range['max']}",
                    'total' => $count,
                ];
            });

            // Data Lokasi Pencaker
            $lokasi_pencaker = DB::table('users')
                ->select('kecamatan', DB::raw('count(*) as total'))
                ->orderBy('total', 'desc')
                ->groupBy('kecamatan')
                ->limit(10)
                ->get();

            // Data Statistik Umum
            $data = [
                'all_jobs' => Job::withoutEdited()->count(),
                'active_jobs' => Job::withoutEdited()->where('status', 'active')->count(),
                'expire_jobs' => Job::withoutEdited()->where('status', 'expired')->count(),
                'pending_jobs' => Job::withoutEdited()->where('status', 'pending')->count(),
                'candidates' => Candidate::count(),
                'companies' => Company::count(),
            ];

            // Generate PDF
            $pdf = PDF::loadView('backend.exports.dashboard', compact(
                'tahun', 'bulanList', 'application_status', 'application_trend',
                'pendidikan_pencaker', 'umur_pencaker', 'lokasi_pencaker', 'data'
            ));

            return $pdf->download('statistik-dashboard-' . $tahun . '.pdf');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /*
    * Mark all notifications as read
    *
    * @return Response
    */
    public function notificationRead()
    {
        foreach (auth()->user()->unreadNotifications as $notification) {
            $notification->markAsRead();
        }

        return response()->json(true);
    }

    /*
    * Get all notifications
    *
    * @return Response
    */
    public function allNotifications()
    {

        $notifications = auth()->user()->notifications()->paginate(20);

        return view('backend.notifications', compact('notifications'));
    }

    /*
    * Format earnings data
    *
    * @param object $data
    * @return array
    */
    private function formatEarnings(object $data)
    {
        $amountArray = [];
        $monthArray = [];

        foreach ($data as $value) {
            array_push($amountArray, $value->amount);
            array_push($monthArray, $value->month);
        }

        return ['amount' => $amountArray, 'months' => $monthArray];
    }

    /*
    * Download transaction invoice
    *
    * @param Earning $transaction
    * @return Response
    */
    public function downloadTransactionInvoice(Earning $transaction)
    {
        $transaction = $transaction->load('plan', 'company.user.contactInfo');
        $pdf = PDF::loadView('frontend.pages.invoice.download-invoice', compact('transaction'))->setOptions(['defaultFont' => 'sans-serif']);

        return $pdf->stream();

        return $pdf->download('invoice_'.$transaction->order_id.'.pdf');
    }

    /*
    * View transaction invoice
    *
    * @param Earning $transaction
    * @return Response
    */
    public function viewTransactionInvoice(Earning $transaction)
    {
        $transaction = $transaction->load('plan', 'company.user.contactInfo');

        return view('frontend.pages.invoice.preview-invoice', compact('transaction'));
    }


    /**
     * Menampilkan halaman dokumen pelamar di backend
     *
     * @param  int  $candidateId
     * @return \Illuminate\Http\Response
     */
    public function showCandidateDocuments($candidateId)
    {
        try {
            // Cari data pelamar berdasarkan ID
            $candidate = Candidate::with('user')->findOrFail($candidateId);

            // Ambil CV yang di-upload
            $resume = CandidateResume::where('candidate_id', $candidate->id)->first();

            // Log untuk debugging
            \Log::info('Dokumen Pencaker', [
                'candidate_id' => $candidateId,
                'resume' => $resume ? $resume->file : 'Tidak ada',
                'ak1' => $candidate->user->ak1 ?? 'Tidak ada'
            ]);

            return view('backend.candidate.document', compact('candidate', 'resume'));

        } catch (\Exception $e) {
            // Cek log error dan tampilkan pesan kesalahan yang lebih spesifik
            \Log::error('Error in showCandidateDocuments: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Download CV pelamar
     *
     * @param  int  $candidateId
     * @return \Illuminate\Http\Response
     */
    public function downloadCV($candidateId)
    {
        try {
            $resume = CandidateResume::where('candidate_id', $candidateId)->first();

            if ($resume) {
                $filePath = 'uploads/file/candidates/' . $resume->file; // Path relatif dari folder public
                $fullFilePath = public_path($filePath); // Menghasilkan path absolut dari folder public

                \Log::info('File path: ' . $fullFilePath); // Log path untuk debugging

                // Periksa jika file ada
                if (file_exists($fullFilePath)) {
                    return response()->download($fullFilePath);
                } else {
                    return redirect()->back()->with('error', 'File CV tidak ditemukan.');
                }
            } else {
                return redirect()->back()->with('error', 'Resume tidak ditemukan untuk pelamar ini.');
            }
        } catch (\Exception $e) {
            \Log::error('Error in downloadCV: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Hide setup guide by marking all tasks as completed
     */
    public function hideSetupGuide()
    {
        try {
            // Update semua setup guide task menjadi completed
            \Modules\SetupGuide\Entities\SetupGuide::query()->update(['status' => 1]);

            // Clear cache
            \Illuminate\Support\Facades\Cache::forget('app_setup');

            return response()->json(['success' => true, 'message' => 'Setup guide berhasil disembunyikan']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal menyembunyikan setup guide: ' . $e->getMessage()], 500);
        }
    }
}
