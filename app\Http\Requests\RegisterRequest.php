<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:50', 'regex:/^[\pL\s]+$/u'],
            'nik' => ['required', 'numeric', 'digits_between:13,16'],
            'provinsi' => ['required', 'exists:provinsi,id'],
            'kabupaten_kota' => ['required', 'exists:kabupaten_kota,id'],
            'kecamatan' => ['required', 'exists:kecamatan,id'],
            'kelurahan' => ['required', 'exists:kelurahan,id'],
            'tempat_lahir' => ['required', 'string', 'regex:/^[\pL\s]+$/u'],
            'tanggal_lahir' => ['required', 'date'],
            'jenis_kelamin' => ['required', 'in:male,female'],
            'status_perkawinan' => ['required', 'in:single,married,divorced,widowed'],
            'agama' => ['required', 'in:islam,christian,catholic,hindu,buddhist,other'],
            'pendidikan' => ['required', 'in:sd,smp,sma,undergraduate,postgraduate'],
            'no_hp' => ['nullable', 'numeric', 'regex:/^(08\d{8,11})$/'],
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required', 'string', 'min:6'],
        ];
    }

    /**
     * Get the custom messages for the validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.regex' => 'Nama lengkap hanya boleh terdiri dari huruf dan spasi.',
            'nik.digits_between' => 'NIK harus terdiri dari antara 1 hingga 16 digit.',
            'provinsi.exists' => 'Provinsi yang dipilih tidak valid.',
            'kabupaten_kota.exists' => 'Kota yang dipilih tidak valid.',
            'kecamatan.exists' => 'Kecamatan yang dipilih tidak valid.',
            'kelurahan.exists' => 'Kelurahan yang dipilih tidak valid.',
            'tempat_lahir.regex' => 'Tempat lahir hanya boleh terdiri dari huruf.',
            'jenis_kelamin.in' => 'Jenis kelamin yang dipilih tidak valid.',
            'status_perkawinan.in' => 'Status perkawinan yang dipilih tidak valid.',
            'agama.in' => 'Agama yang dipilih tidak valid.',
            'pendidikan.in' => 'Pendidikan yang dipilih tidak valid.',
            'no_hpregex' => 'Nomor telepon harus dimulai dengan 08.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah digunakan.',
            'password.min' => 'Password harus memiliki minimal 6 karakter.',
        ];
    }

    /**
     * Format the phone number if it starts with 08 or 02.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $data = $this->all();

        if (isset($data['no_hp'])) {
            // Jika nomor telepon dimulai dengan '08', ubah menjadi '628'
            if (strpos($data['no_hp'], '08') === 0) {
                $data['no_hp'] = '628' . substr($data['no_hp'], 2);
            }
            // Jika nomor telepon dimulai dengan '02', ubah menjadi '622'
            elseif (strpos($data['no_hp'], '02') === 0) {
                $data['no_hp'] = '622' . substr($data['no_hp'], 2);
            }
        }

        // Gabungkan data yang sudah diubah dengan data request
        $this->merge($data);
    }

}

