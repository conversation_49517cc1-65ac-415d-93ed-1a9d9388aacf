@extends('backend.layouts.auth')
@section('content')
    <p class="login-box-msg text-white-50 text-lg text-bold">{{ __('Selamat Datang!') }}</p>

    <form method="POST" action="{{ route('admin.login') }}">
        @csrf
        <div class="input-group mb-3">
            <input type="email" class="form-control @error('email') is-invalid @enderror" name="email"
                value="" placeholder="{{ __('email') }}">
            <div class="input-group-append">
                <div class="input-group-text">
                    <span class="fas fa-envelope"></span>
                </div>
            </div>
            @error('email')
                <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
            @enderror
        </div>
        <div class="input-group mb-3">
            <input type="password" class="form-control @error('password') is-invalid @enderror" name="password" id="password"
                placeholder="{{ __('password') }}" value="">
            <div class="input-group-append">
                <div class="input-group-text">
                    <span class="fas fa-lock"></span>
                </div>
            </div>
            <div class="input-group-append">
                <div class="input-group-text" onclick="togglePassword('password','eyeIcon')" id="eyeIcon" style="cursor: pointer;">
                    <span class="fas fa-eye @error('password') text-danger @enderror"></span>
                </div>
            </div>
            @error('password')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong></span>
            @enderror
        </div>
        <div class="d-flex justify-content-between align-items-center">
            <div class="icheck-primary text-white-50">
                <input type="checkbox" id="remember">
                <x-forms.label name="remember_me" :required="false" for="remember" />
            </div>

            <a href="{{ route('admin.password.request') }}">{{ __('Lupa Password?') }}</a>
        </div>
        @if (config('captcha.active'))
            <div class="input-group mt-3 text-center">
                {!! NoCaptcha::display() !!}
                @if ($errors->has('g-recaptcha-response'))
                    <span class="text-danger text-sm">
                        <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                    </span>
                @endif
            </div>
        @endif
        <button type="submit" class="btn btn-primary btn-block mt-4">
            {{ __('sign_in') }}
            <i class="fas fa-arrow-right"></i>
        </button>
        <br />
        <p class="text-center text-white-50">Tidak memiliki akses?  <br />Kembali ke <a class="text-bold" href="/">Beranda</a></p>
    </form>


@endsection

@section('backend_auth_script')
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script>
        function togglePassword(inputId, iconId) {
            var input = document.getElementById(inputId);
            var eyeIcon = document.getElementById(iconId);
            var eyeSpan = eyeIcon.querySelector('span');

            if (input.type === 'password') {
                input.type = 'text';
                eyeSpan.className = 'fas fa-eye-slash @error('password') text-danger @enderror';
            } else {
                input.type = 'password';
                eyeSpan.className = 'fas fa-eye @error('password') text-danger @enderror';
            }
        }
    </script>
@endsection
