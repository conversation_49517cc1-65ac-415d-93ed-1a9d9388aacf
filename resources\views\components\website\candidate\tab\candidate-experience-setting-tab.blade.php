@props(['experiences'])
<div class="tw-flex rt-mb-32 lg:tw-mt-0 tw-items-center tw-justify-between">
    <h3 class="f-size-18 tw-flex-shrink-0 lh-1 m-0">{{ __('experience') }}</h3>
    <button id="addExperience" type="button" class="btn btn-primary">
        {{ __('add_experience') }}
    </button>
</div>
<div class="db-job-card-table -tw-mx-2 tw-pb-16">
    <table class="tw-px-2">
        <thead>
            <tr>
                <th class="!tw-text-base !tw-font-medium">{{ __('company') }}</th>
                <th class="!tw-text-base !tw-font-medium">{{ __('Divisi') }}</th>
                <th class="!tw-text-base !tw-font-medium">{{ __('Jabatan') }}</th>
                <th class="!tw-text-base !tw-font-medium">{{ __('period') }}</th>
                <th class="!tw-text-base !tw-font-medium tw-text-right">{{ __('action') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($experiences as $experience)
                <tr>
                    <td>
                        @if($experience->fresh_graduate)
                            <span class="badge bg-success">Fresh Graduate</span>
                        @else
                            {{ $experience->company }}
                        @endif
                    </td>
                    <td>
                        @if($experience->fresh_graduate)
                            <span class="text-muted">Fresh Graduate</span>
                        @else
                            {{ $experience->department }}
                        @endif
                    </td>
                    <td>
                        @if($experience->fresh_graduate)
                            <span class="text-muted">Tidak Berpengalaman</span>
                        @else
                            {{ $experience->designation }}
                        @endif
                    </td>
                    <td>
                        @if($experience->fresh_graduate)
                            <span class="text-muted">-</span>
                        @else
                            {{ formatTime($experience->start, 'M Y') }} -
                            {{ $experience->currently_working ?  __('currently_working') :formatTime($experience->end, 'M Y') }}
                        @endif
                    </td>
                    <td>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-icon" id="dropdownMenuButton5"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M12 13.125C12.6213 13.125 13.125 12.6213 13.125 12C13.125 11.3787 12.6213 10.875 12 10.875C11.3787 10.875 10.875 11.3787 10.875 12C10.875 12.6213 11.3787 13.125 12 13.125Z"
                                        fill="#767F8C" stroke="#767F8C" />
                                    <path
                                        d="M12 6.65039C12.6213 6.65039 13.125 6.14671 13.125 5.52539C13.125 4.90407 12.6213 4.40039 12 4.40039C11.3787 4.40039 10.875 4.90407 10.875 5.52539C10.875 6.14671 11.3787 6.65039 12 6.65039Z"
                                        fill="#767F8C" stroke="#767F8C" />
                                    <path
                                        d="M12 19.6094C12.6213 19.6094 13.125 19.1057 13.125 18.4844C13.125 17.8631 12.6213 17.3594 12 17.3594C11.3787 17.3594 10.875 17.8631 10.875 18.4844C10.875 19.1057 11.3787 19.6094 12 19.6094Z"
                                        fill="#767F8C" stroke="#767F8C" />
                                </svg>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end company-dashboard-dropdown"
                                aria-labelledby="dropdownMenuButton5">
                                <li>
                                    <a href="javascript:void(0)" class="dropdown-item" onclick="experienceDetail({{ json_encode($experience) }}, '{{ $experience->start ? date('m-Y', strtotime($experience->start)) : '' }}', '{{ $experience->end ? date('m-Y', strtotime($experience->end)) : '' }}')"
                                        <x-svg.edit-icon/>
                                        {{ __('Edit') }}
                                    </a>
                                </li>
                                <li>
                                    <form method="POST" action="{{ route('candidate.experiences.destroy', $experience->id) }}">
                                        @csrf
                                        @method('Delete')
                                        <button type="submit" class="dropdown-item"
                                        onclick="return confirm('{{ __('Anda yakin ingin menghapus pengalaman kerja ini?') }}');">
                                        {{-- onclick="return confirm('{{ __('are_you_sure_you_want_to_delete_this_item') }}');"> --}}
                                            <x-svg.trash-icon/>
                                            {{ __('delete') }}
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center">
                        <x-svg.not-found-icon />
                        <p class="mt-4">{{ __('no_data_found') }}</p>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('frontend_links')
<link rel="stylesheet" href="{{ asset('frontend') }}/assets/css/bootstrap-datepicker.min.css">
<style>
    #addExperienceModal .modal-dialog,
    #editExperienceModal .modal-dialog{
        z-index:999999 !important;
        max-width: 950px !important;
        padding: 20px !important;
    }
</style>

@endpush

@push('frontend_scripts')
    <script src="{{ asset('frontend/assets/js/bootstrap-datepicker.min.js') }}"></script>
    @if (app()->getLocale() == 'ar')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.ar.min.js
    "></script>
    @endif
    <script>

        $('#addExperience').on('click', function(){
            // Check if there are any existing experiences
            @php
                $hasFreshGraduate = $experiences->where('fresh_graduate', 1)->count() > 0;
                $hasWorkExperience = $experiences->where('fresh_graduate', 0)->count() > 0;
            @endphp

            var hasFreshGraduate = {{ $hasFreshGraduate ? 'true' : 'false' }};
            var hasWorkExperience = {{ $hasWorkExperience ? 'true' : 'false' }};

            if (hasFreshGraduate) {
                alert('Tidak dapat menambah pengalaman kerja karena status Fresh Graduate sudah ada. Hapus status Fresh Graduate terlebih dahulu.');
                return;
            }

            $('#addExperienceModal').modal('show');
        });

        //  $(".date_picker").attr("autocomplete", "off");

        // //init datepicker
        // $('.date_picker').off('focus').datepicker({
        //     format: 'd-m-yyyy',
        //     isRTL: "{{ app()->getLocale() == 'ar' ? true : false }}",
        //     language: "{{ app()->getLocale() }}",
        // }).on('click',
        //     function() {
        //         $(this).datepicker('show');
        //     }
        // );
        $('.date_picker').datepicker({
            format: "mm-yyyy",
            viewMode: "months",
            minViewMode: "months",
            autoclose: true
        }).on('changeDate', function(e) {
            // Get the selected date
            var date = $(this).val();
            if (date) {
                // Convert mm-yyyy to yyyy-mm format for database
                var parts = date.split('-');
                if (parts.length === 2) {
                    var month = parts[0];
                    var year = parts[1];
                    // Create the database format (yyyy-mm)
                    var dbFormat = year + '-' + month;

                    // Get the corresponding hidden field
                    var fieldName = $(this).attr('id');
                    var hiddenField = $('#' + fieldName + '_db');

                    // Update the hidden field value
                    if (hiddenField.length > 0) {
                        hiddenField.val(dbFormat);
                    }
                }
            }
        });

        // Process all date fields on form submit
        $('form').on('submit', function(e) {
            $(this).find('.date_picker').each(function() {
                var date = $(this).val();
                if (date) {
                    // Convert mm-yyyy to yyyy-mm format for database
                    var parts = date.split('-');
                    if (parts.length === 2) {
                        var month = parts[0];
                        var year = parts[1];
                        var dbFormat = year + '-' + month;

                        // Update the hidden field
                        var fieldName = $(this).attr('id');
                        var hiddenField = $('#' + fieldName + '_db');
                        if (hiddenField.length > 0) {
                            hiddenField.val(dbFormat);
                        }
                    }
                }
            });
        });

        function closeAddExperienceModal(){
            $('#addExperienceModal').find('form')[0].reset();
            $('#fresh-graduate-switch-add').prop('checked', false);
            $('#experience-fields-add').show();
            $('#company-add, #department-add, #designation-add, #add_experience_start').prop('required', true);
            $('#addExperienceModal').modal('hide')
        }

        function closeEditExperienceModal(){
            $('#editExperienceModal').find('form')[0].reset();
            $('#fresh-graduate-switch-edit').prop('checked', false);
            $('#experience-fields-edit').show();
            $('#experience-modal-company, #experience-modal-department, #experience-modal-designation, #experience-modal-start').prop('required', true);
            $('#editExperienceModal').modal('hide')
        }

        // Handle the currently working checkbox
        $(document).ready(function() {
            // Handle checkbox change
            $('#experience-modal-checkbox_create, #experience-modal-checkbox_edit').on('change', function() {
                var endDateField = $(this).closest('form').find('.experience_end_date');
                if ($(this).is(':checked')) {
                    endDateField.hide();
                    // Clear the end date value
                    endDateField.find('input[name="end_display"]').val('');
                    endDateField.find('input[name="end"]').val('');
                } else {
                    endDateField.show();
                }
            });

            // Initialize checkbox state on page load
            if ($('#experience-modal-checkbox_create').is(':checked')) {
                $('#experience-modal-checkbox_create').closest('form').find('.experience_end_date').hide();
            }
            if ($('#experience-modal-checkbox_edit').is(':checked')) {
                $('#experience-modal-checkbox_edit').closest('form').find('.experience_end_date').hide();
            }

            // Trigger change event on edit modal show
            $('#editExperienceModal').on('shown.bs.modal', function() {
                $('#experience-modal-checkbox_edit').trigger('change');
            });
        });

        function experienceDetail(experience, start, end) {
            $('#experience-modal-id').val(experience.id);
            $('#experience-modal-company').val(experience.company);
            $('#experience-modal-department').val(experience.department);
            $('#experience-modal-designation').val(experience.designation);

            // Set display values
            $('#experience-modal-start').val(start);
            $('#experience-modal-end').val(end);

            // Convert start date: mm-yyyy to yyyy-mm format for database
            var startParts = start.split('-');
            if (startParts.length === 2) {
                var startMonth = startParts[0];
                var startYear = startParts[1];
                var startDbFormat = startYear + '-' + startMonth;

                // Update hidden field value
                $('#experience-modal-start_db').val(startDbFormat);
            }

            // Convert end date: mm-yyyy to yyyy-mm format for database (if not currently working)
            if (end && !experience.currently_working) {
                var endParts = end.split('-');
                if (endParts.length === 2) {
                    var endMonth = endParts[0];
                    var endYear = endParts[1];
                    var endDbFormat = endYear + '-' + endMonth;

                    // Update hidden field value
                    $('#experience-modal-end_db').val(endDbFormat);
                }
            }

            $('#experience-responsibilities').val(experience.responsibilities);
            $('#experience-modal-checkbox_edit').prop("checked", experience.currently_working ? true:false);

            // Handle fresh graduate status
            if (experience.fresh_graduate) {
                $('#fresh-graduate-switch-edit').prop('checked', true);
                $('#experience-fields-edit').hide();
                $('#experience-fields-edit input, #experience-fields-edit textarea').prop('required', false);
            } else {
                $('#fresh-graduate-switch-edit').prop('checked', false);
                $('#experience-fields-edit').show();
                $('#experience-modal-company, #experience-modal-department, #experience-modal-designation, #experience-modal-start').prop('required', true);
            }

            $('#editExperienceModal').modal('show');
        }
    </script>
@endpush
