<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\Message;
use App\Models\MessageThread;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    /**
     * Display inbox for candidate or company
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $threadId = $request->query('pesan_id');
        $page = $request->query('page', 1);
        $unreadOnly = $request->query('unread') === '1';
        $perPage = 10;
        $search = $request->query('cari');

        // Get threads based on user role
        $query = MessageThread::query();

        if ($user->role == 'company') {
            $query->where('company_id', $user->company->id);
        } elseif ($user->role == 'candidate') {
            $query->where('candidate_id', $user->candidate->id);
        }

        // Apply unread filter if needed
        if ($unreadOnly) {
            $query->whereHas('messages', function ($q) use ($user) {
                $q->where('read', false)
                  ->where('receiver_id', $user->id);
            });
        }

        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhereHas('messages', function($q) use ($search) {
                      $q->where('body', 'like', "%{$search}%");
                  })
                  ->orWhereHas('company.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('candidate.user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Get total count for pagination
        $totalThreads = $query->count();
        $totalPages = ceil($totalThreads / $perPage);

        // Get threads with relationships
        $threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) use ($user) {
                $q->where('receiver_id', $user->id);
            }
        ])->latest()->skip(($page - 1) * $perPage)->take($perPage)->get();

        // Get selected thread if provided
        $selectedThread = null;
        $messages = [];

        if ($threadId) {
            $selectedThread = MessageThread::with(['company.user', 'candidate.user', 'job'])->find($threadId);

            if ($selectedThread) {
                // Get the 5 most recent messages
                $messages = Message::where('message_thread_id', $threadId)
                    ->with([
                        'sender.company',
                        'sender.candidate',
                        'receiver.company',
                        'receiver.candidate'
                    ])
                    ->orderBy('created_at', 'desc')
                    ->take(5)
                    ->get()
                    ->sortBy('created_at');

                // Count total messages for "load more" button
                $totalMessages = Message::where('message_thread_id', $threadId)->count();
                $hasMoreMessages = $totalMessages > 5;

                // Mark messages as read
                Message::where('message_thread_id', $threadId)
                    ->where('receiver_id', $user->id)
                    ->where('read', false)
                    ->update(['read' => true]);
            }
        }

        // Group messages by date
        $groupedMessages = [];
        foreach ($messages as $message) {
            $date = $message->created_at->format('Y-m-d');
            if (!isset($groupedMessages[$date])) {
                $groupedMessages[$date] = [];
            }
            $groupedMessages[$date][] = $message;
        }

        $data = [
            'threads' => $threads,
            'selectedThread' => $selectedThread,
            'groupedMessages' => $groupedMessages,
            'hasMoreMessages' => $hasMoreMessages ?? false,
            'totalMessages' => $totalMessages ?? 0,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'unreadOnly' => $unreadOnly,
            'perPage' => $perPage,
            'search' => $search,
        ];

        if ($user->role == 'candidate') {
            return view('frontend.pages.candidate.inbox-no-ajax', $data);
        } else {
            return view('frontend.pages.company.inbox-no-ajax', $data);
        }
    }

    /**
     * Load more messages for a thread
     */
    public function loadMoreMessages(Request $request)
    {
        $threadId = $request->input('thread_id');
        $skip = $request->input('skip', 5); // Skip the 5 most recent messages already loaded

        $messages = Message::where('message_thread_id', $threadId)
            ->with([
                'sender.company',
                'sender.candidate',
                'receiver.company',
                'receiver.candidate'
            ])
            ->orderBy('created_at', 'desc')
            ->skip($skip)
            ->take(5)
            ->get()
            ->sortBy('created_at');

        // Group messages by date
        $groupedMessages = [];
        foreach ($messages as $message) {
            $date = $message->created_at->format('Y-m-d');
            if (!isset($groupedMessages[$date])) {
                $groupedMessages[$date] = [];
            }
            $groupedMessages[$date][] = $message;
        }

        return response()->json([
            'messages' => view('frontend.pages.messages.partials.message-items', [
                'groupedMessages' => $groupedMessages,
                'startIndex' => $skip
            ])->render(),
            'hasMoreMessages' => $messages->count() == 5
        ]);
    }

    /**
     * Reply to a message
     */
    public function reply(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'message' => 'nullable|string',
            'attachment' => 'nullable|file|max:5120', // 5MB max
            'attachment_type' => 'nullable|in:document,image',
        ]);

        // Require either message or attachment
        if (empty($request->message) && !$request->hasFile('attachment')) {
            return response()->json(['error' => 'Message or attachment is required'], 422);
        }

        $user = Auth::user();
        $threadId = $request->input('thread_id');
        $thread = MessageThread::find($threadId);

        if (!$thread) {
            return response()->json(['error' => 'Thread not found'], 404);
        }

        // Verify user has access to this thread
        if ($user->role == 'company' && $thread->company_id != $user->company->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        } elseif ($user->role == 'candidate' && $thread->candidate_id != $user->candidate->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Determine receiver based on user role
        $receiverId = null;
        if ($user->role == 'company') {
            if ($thread->candidate_id) {
                $receiverId = $thread->candidate->user_id;
            } elseif ($thread->is_admin_thread) {
                // Jika ini adalah thread admin, penerima adalah admin
                $admin = \App\Models\Admin::first();
                if ($admin) {
                    $receiverId = $admin->id;
                } else {
                    // Fallback jika tidak ada admin
                    $receiverId = User::where('role', 'admin')->first()->id ?? null;
                }
            }
        } elseif ($user->role == 'candidate') {
            if ($thread->company_id) {
                $receiverId = $thread->company->user_id;
            } elseif ($thread->is_admin_thread) {
                // Jika ini adalah thread admin, penerima adalah admin
                $admin = \App\Models\Admin::first();
                if ($admin) {
                    $receiverId = $admin->id;
                } else {
                    // Fallback jika tidak ada admin
                    $receiverId = User::where('role', 'admin')->first()->id ?? null;
                }
            }
        }

        // Handle attachment if present
        $attachmentData = null;
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $attachmentType = $request->input('attachment_type');

            // Validate file type
            if ($attachmentType == 'document' && $file->getClientMimeType() != 'application/pdf') {
                return response()->json(['error' => 'Only PDF files are allowed for documents'], 422);
            }

            if ($attachmentType == 'image' && !in_array($file->getClientMimeType(), ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
                return response()->json(['error' => 'Only image files are allowed for images'], 422);
            }

            // Store the file
            $path = $file->store('uploads/messages', 'public');

            $attachmentData = [
                [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'type' => $attachmentType,
                    'size' => $file->getSize()
                ]
            ];
        }

        // Create the message
        $message = Message::create([
            'message_thread_id' => $threadId,
            'sender_id' => $user->id,
            'sender_type' => null, // Pengguna biasa, bukan admin
            'receiver_id' => $receiverId,
            'body' => $request->input('message') ?? '',
            'type' => 'umum',
            'can_reply' => true,
            'read' => false,
            'attachment' => $attachmentData
        ]);

        // Load relationships for the new message
        $message->load([
            'sender.company',
            'sender.candidate',
            'receiver.company',
            'receiver.candidate'
        ]);

        return response()->json([
            'success' => true,
            'message' => view('frontend.pages.messages.partials.single-message', [
                'message' => $message,
                'index' => 0
            ])->render()
        ]);
    }

    /**
     * Get unread message count for the current user
     */
    public function getUnreadCount()
    {
        $user = Auth::user();

        $query = Message::where('receiver_id', $user->id)
            ->where('read', false);

        $count = $query->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Get threads for AJAX pagination
     */
    public function getThreadsAjax(Request $request)
    {
        $user = Auth::user();
        $page = $request->input('page', 1);
        $unreadOnly = $request->input('unread') === '1';
        $perPage = 5; // Menampilkan 5 pesan per halaman

        // Get threads based on user role
        $query = MessageThread::query();

        if ($user->role == 'company') {
            $query->where('company_id', $user->company->id);
        } elseif ($user->role == 'candidate') {
            $query->where('candidate_id', $user->candidate->id);
        }

        // Apply unread filter if needed
        if ($unreadOnly) {
            $query->whereHas('messages', function ($q) use ($user) {
                $q->where('read', false)
                  ->where('receiver_id', $user->id);
            });
        }

        // Sort by latest message (komunikasi terbaru)
        $query->orderByDesc(function ($query) {
            $query->select('created_at')
                ->from('messages')
                ->whereColumn('message_thread_id', 'message_threads.id')
                ->orderBy('created_at', 'desc')
                ->limit(1);
        });

        // Get total count for pagination
        $totalThreads = $query->count();
        $totalPages = ceil($totalThreads / $perPage);

        // Get threads with relationships
        $threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) use ($user) {
                $q->where('receiver_id', $user->id);
            }
        ])->skip(($page - 1) * $perPage)->take($perPage)->get();

        // Render the threads HTML
        $view = $user->role == 'candidate' ? 'frontend.pages.candidate.partials.thread-list' : 'frontend.pages.company.partials.thread-list';

        $html = view($view, [
            'threads' => $threads,
            'unreadOnly' => $unreadOnly
        ])->render();

        return response()->json([
            'html' => $html,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'hasMorePages' => $page < $totalPages,
        ]);
    }
}
