<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CandidateResume;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CandidateResumeController extends Controller
{
    /**
     * Preview candidate resume
     *
     * @param CandidateResume $resume
     * @return \Illuminate\Http\Response
     */
    public function preview(CandidateResume $resume)
    {
        abort_if(!userCan('job.view'), 403);

        if (!$resume || !$resume->file) {
            abort(404);
        }

        $path = $resume->file;
        $fullPath = public_path($path);

        if (!file_exists($fullPath)) {
            abort(404);
        }

        $content = file_get_contents($fullPath);
        $type = mime_content_type($fullPath);

        return response($content, 200)->header('Content-Type', $type);
    }
}
