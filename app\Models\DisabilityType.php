<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DisabilityType extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'icon'];

    /**
     * Get the jobs that are disability friendly for this type
     */
    public function jobs()
    {
        return $this->belongsToMany(Job::class, 'job_disability_type');
    }
}
