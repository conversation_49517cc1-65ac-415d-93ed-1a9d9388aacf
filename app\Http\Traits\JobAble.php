<?php

namespace App\Http\Traits;

use App\Models\Benefit;
use App\Models\BenefitTranslation;
use App\Models\Education;
use App\Models\EducationTranslation;
use App\Models\Experience;
use App\Models\ExperienceTranslation;
use App\Models\Job;
use App\Models\JobCategory;
use App\Models\JobRole;
use App\Models\JobType;
use App\Models\JobTypeTranslation;
use App\Models\Skill;
use App\Models\SkillTranslation;
use App\Models\Tag;
use App\Models\TagTranslation;
use App\Notifications\Website\Candidate\ApplyJobNotification;
use App\Notifications\Website\Candidate\BookmarkJobNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\Location\Entities\Country;

trait JobAble
{
    protected function getJobs($request)
    {
        $query = $this->filterJobs($request);

        // Apply sorting if provided
        if ($request->has('sort_by')) {
            switch ($request->sort_by) {
                case 'newest':
                    $query->latest();
                    break;
                case 'oldest':
                    $query->oldest();
                    break;
                default:
                    $query->latest();
                    break;
            }
        } else {
            $query->latest();
        }

        // Optimize query by selecting only needed fields
        $query->select([
            'id', 'title', 'company_id', 'category_id', 'job_type_id', 'education_id',
            'min_salary', 'max_salary', 'salary_mode', 'custom_salary', 'deadline',
            'locality', 'country', 'region', 'district', 'disability_friendly', 'status',
            'slug', 'is_remote', 'waiting_for_edit_approval', 'created_at', 'featured'
        ]);

        // Use a separate optimized query for featured jobs
        $featured_query = Job::with(['company.user', 'job_type:id,name'])
            ->select([
                'id', 'title', 'company_id', 'job_type_id', 'min_salary', 'max_salary',
                'salary_mode', 'custom_salary', 'deadline', 'slug', 'status', 'featured'
            ])
            ->where('featured', 1)
            ->where('status', 'active')
            ->latest()
            ->take(9);

        // Cache frequently used data
        $countries = cache()->remember('countries_list', 60*24, function() {
            return Country::all(['id', 'name', 'slug']);
        });

        $categories = cache()->remember('job_categories_list', 60*24, function() {
            return JobCategory::with('translations')->get(['id', 'slug'])->sortBy(function($category) {
                return $category->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $job_roles = cache()->remember('job_roles_list', 60*24, function() {
            return JobRole::with('translations')->get(['id'])->sortBy(function($role) {
                return $role->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $job_types = cache()->remember('job_types_list', 60*24, function() {
            return JobType::with('translations')->get(['id'])->sortBy(function($type) {
                return $type->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $experiences = cache()->remember('experiences_list', 60*24, function() {
            return Experience::with('translations')->get(['id'])->sortBy(function($experience) {
                return $experience->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $educations = cache()->remember('educations_list', 60*24, function() {
            return Education::with('translations')->get(['id'])->sortBy(function($education) {
                return $education->name; // Ini akan mengakses nama melalui translasi
            });
        });

        $salary_stats = cache()->remember('salary_stats', 60*24, function() {
            return [
                'max_salary' => \DB::table('jobs')->max('max_salary'),
                'min_salary' => \DB::table('jobs')->max('min_salary')
            ];
        });

        $jobs = $query->paginate(9)->withQueryString();
        $featured_jobs = $featured_query->get();

        return [
            'total_jobs' => $jobs->total(),
            'jobs' => $jobs,
            'featured_jobs' => $featured_jobs,
            'countries' => $countries,
            'categories' => $categories,
            'job_roles' => $job_roles,
            'max_salary' => $salary_stats['max_salary'],
            'min_salary' => $salary_stats['min_salary'],
            'experiences' => $experiences,
            'educations' => $educations,
            'job_types' => $job_types,
            'skills' => cache()->remember('skills_list', 60*24, function() {
                return Skill::with('translations')->get(['id'])->sortBy(function($skill) {
                    return $skill->name; // Ini akan mengakses nama melalui translasi
                });
            }),
            'popularTags' => cache()->remember('popular_tags', 60*24, function() {
                return $this->popularTags();
            }),
        ];
    }

    /**
     * Returns a list of jobs with pagination for the load more button.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Support\Collection
     */
    protected function moreJobs($request)
    {
        $jobs = $this->filterJobs($request)->latest()->take(18)->get();

        return $jobs;
    }

    /**
     * Returns a query builder instance with filters applied from the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function filterJobs($request)
    {
        if (auth()->user() && auth()->user()->role == 'candidate' && auth()->user()->candidate) {
            $candidateId = auth()->user()->candidate->id;

            // Log untuk debugging
            \Log::info("filterJobs - Candidate ID: {$candidateId}");

            // Gunakan join untuk performa yang lebih baik
            $query = Job::with('company.user', 'category', 'job_type:id,name')
                ->select('jobs.*')
                ->selectRaw('
                    (SELECT COUNT(*) FROM bookmark_candidate_job
                     WHERE bookmark_candidate_job.job_id = jobs.id) as bookmark_jobs_count,
                    (SELECT COUNT(*) FROM applied_jobs
                     WHERE applied_jobs.job_id = jobs.id) as applied_jobs_count,
                    (SELECT COUNT(*) FROM bookmark_candidate_job
                     WHERE bookmark_candidate_job.job_id = jobs.id
                     AND bookmark_candidate_job.candidate_id = ?) as bookmarked,
                    (SELECT COUNT(*) FROM applied_jobs
                     WHERE applied_jobs.job_id = jobs.id
                     AND applied_jobs.candidate_id = ?) as applied
                ', [$candidateId, $candidateId])
                ->active()
                ->withoutEdited();

            // Tambahkan event listener untuk mengkonversi nilai bookmarked ke boolean
            $query->macro('get', function($columns = ['*']) use ($query, $candidateId) {
                $results = $query->getQuery()->get($columns);

                foreach ($results as $result) {
                    // Pastikan nilai bookmarked dikonversi ke boolean
                    $isBookmarked = DB::table('bookmark_candidate_job')
                        ->where('job_id', $result->id)
                        ->where('candidate_id', $candidateId)
                        ->exists();

                    $result->bookmarked = $isBookmarked;
                    $result->setAttribute('bookmarked', (bool)$isBookmarked);
                }

                return $results;
            });
        } else {
            $query = Job::with('company.user', 'category', 'job_type:id,name')
                ->select('jobs.*')
                ->selectRaw('
                    (SELECT COUNT(*) FROM bookmark_candidate_job
                     WHERE bookmark_candidate_job.job_id = jobs.id) as bookmark_jobs_count,
                    (SELECT COUNT(*) FROM applied_jobs
                     WHERE applied_jobs.job_id = jobs.id) as applied_jobs_count,
                    0 as bookmarked,
                    0 as applied
                ')
                ->withoutEdited()
                ->active();

            // Tambahkan event listener untuk mengkonversi nilai bookmarked ke boolean
            $query->macro('get', function($columns = ['*']) use ($query) {
                $results = $query->getQuery()->get($columns);

                foreach ($results as $result) {
                    // Pastikan nilai bookmarked dikonversi ke boolean
                    $result->bookmarked = false;
                    $result->setAttribute('bookmarked', false);
                    $result->applied = false;
                    $result->setAttribute('applied', false);
                }

                return $results;
            });
        }

        // company search
        if ($request->has('company') && $request->company != null) {
            $company = $request->company;
            $query->whereHas('company.user', function ($q) use ($company) {
                $q->where('username', $company);
            });
        }

        // Keyword search
        if ($request->has('keyword') && $request->keyword != null) {
            $keyword = $request->get('keyword');
            if (is_array($keyword)) {
                $keyword = $keyword[0];
            }
            $query->where('title', 'LIKE', "%$keyword%");
        }

        // Category filter
        if ($request->has('category') && $request->category != null) {
            $category = JobCategory::where('slug', $request->category)->first();
            $query->when($category, function ($query) use ($category) {
                return $query->where('category_id', $category->id);
            });
        }

        // job role filter
        if ($request->has('job_role') && $request->job_role != null) {
            $query->where('role_id', $request->job_role);
        }

        // Salary filter
        if ($request->has('gaji_min') && $request->gaji_min != null && $request->gaji_min != '') {
            $query->where('min_salary', '>=', $request->gaji_min);
        } else if ($request->has('price_min') && $request->price_min != null && $request->price_min != '') {
            // For backward compatibility
            $query->where('min_salary', '>=', $request->price_min);
        }

        if ($request->has('gaji_max') && $request->gaji_max != null && $request->gaji_max != '') {
            $query->where('max_salary', '<=', $request->gaji_max);
        } else if ($request->has('price_max') && $request->price_max != null && $request->price_max != '') {
            // For backward compatibility
            $query->where('max_salary', '<=', $request->price_max);
        }

        // tags filter
        if ($request->has('tag') && $request->tag != null) {
            $tag = TagTranslation::where('name', $request->tag)->first();

            if ($tag) {
                $query->whereHas('tags', function ($q) use ($tag) {
                    $q->where('job_tag.tag_id', $tag->tag_id);
                });
            }
        }

        // id filter for load more
        if ($request->id) {
            $query->where('id', '<', $request->id);
        }

        // location
        if ($request->has('location') && $request->location != null) {
            $location = $request->location;
            $query->where(function($q) use ($location) {
                $q->where('country', 'LIKE', '%'.$location.'%')
                  ->orWhere('address', 'LIKE', '%'.$location.'%');
            });
        }

        // Kabupaten/Kota filter
        if ($request->has('kabupaten_kota') && $request->kabupaten_kota != null) {
            $query->where('district', $request->kabupaten_kota);
        }

        // Locality/Kecamatan filter
        if ($request->has('kecamatan') && $request->kecamatan != null) {
            $query->where('locality', $request->kecamatan);
        } elseif ($request->has('locality') && $request->locality != null) {
            // For backward compatibility
            $query->where('locality', $request->locality);
        }
        // lat Long
        if ($request->has('lat') && $request->has('long') && $request->lat != null && $request->long != null) {
            session()->forget('selected_country');
            // $query->Where('address', $final_address ? $final_address : '')->orWhere('country', $request->location ? $request->location : '');
        }

        // country
        $selected_country = session()->get('selected_country');

        if ($selected_country && $selected_country != null) {
            $country = selected_country()->name;
            $query->where('country', 'LIKE', "%$country%");
        } else {
            $setting = loadSetting();
            if ($setting->app_country_type == 'single_base') {
                if ($setting->app_country) {
                    $country = Country::where('id', $setting->app_country)->first();
                    if ($country) {
                        $query->where('country', 'LIKE', "%$country->name%");
                    }
                }
            }
        }

        // Sort by ads
        if ($request->has('sort_by') && $request->sort_by != null) {
            switch ($request->sort_by) {
                case 'latest':
                    $query->latest('id');
                    break;
                case 'featured':
                    $query->where('featured', 1)->latest();
                    break;
            }
        }

        // Experience filter
        if ($request->has('experience') && $request->experience != null) {
            $experience_id = ExperienceTranslation::where('name', $request->experience)->value('id');
            $query->where('experience_id', $experience_id);
        }

        // Education filter
        if ($request->has('pendidikan') && $request->pendidikan != null) {
            $education_translation = EducationTranslation::where('name', $request->pendidikan)->first();
            if ($education_translation) {
                $query->where('education_id', $education_translation->education_id);
            }
        } elseif ($request->has('education') && $request->education != null) {
            // For backward compatibility
            $education_translation = EducationTranslation::where('name', $request->education)->first();
            if ($education_translation) {
                $query->where('education_id', $education_translation->education_id);
            }
        }

        // Work type filter
        if ($request->has('is_remote') && $request->is_remote != null) {
            $query->where('is_remote', 1);
        }

        // Job type filter
        if ($request->has('jenis_pekerjaan') && $request->jenis_pekerjaan != null) {
            $job_type_translation = JobTypeTranslation::where('name', $request->jenis_pekerjaan)->first();
            if ($job_type_translation) {
                $query->where('job_type_id', $job_type_translation->job_type_id);
            }
        } elseif ($request->has('job_type') && $request->job_type != null) {
            // For backward compatibility
            $job_type_translation = JobTypeTranslation::where('name', $request->job_type)->first();
            if ($job_type_translation) {
                $query->where('job_type_id', $job_type_translation->job_type_id);
            }
        }

        // SKill filter
        if ($request->has('skills') && $request->skills != null) {
            $skills = SkillTranslation::where('name', $request->skills)->first();

            if ($skills) {
                $query->whereHas('skills', function ($q) use ($skills) {
                    $q->where('job_skills.skill_id', $skills->skill_id);
                });
            }
        }

        // Disability friendly filter
        if ($request->has('ramah_disabilitas') && $request->ramah_disabilitas != null) {
            $query->where('disability_friendly', 1);
        } elseif ($request->has('disability_friendly') && $request->disability_friendly != null) {
            // For backward compatibility
            $query->where('disability_friendly', 1);
        }

        return $query;
    }

    /**
     * Get jobs based on category slug.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $slug
     * @return array
     */
    private function getJobsCategory($request, $slug)
    {
        // Log untuk debugging
        \Log::info("getJobsCategory dipanggil dengan slug: {$slug}");

        if (auth()->user() && auth()->user()->role == 'candidate' && auth()->user()->candidate) {
            $candidateId = auth()->user()->candidate->id;

            // Gunakan join untuk performa yang lebih baik
            $query = Job::with('company.user', 'job_type:id,name')
                ->select('jobs.*')
                ->selectRaw('
                    (SELECT COUNT(*) FROM bookmark_candidate_job
                     WHERE bookmark_candidate_job.job_id = jobs.id) as bookmark_jobs_count,
                    (SELECT COUNT(*) FROM applied_jobs
                     WHERE applied_jobs.job_id = jobs.id) as applied_jobs_count,
                    (SELECT COUNT(*) FROM bookmark_candidate_job
                     WHERE bookmark_candidate_job.job_id = jobs.id
                     AND bookmark_candidate_job.candidate_id = ?) as bookmarked,
                    (SELECT COUNT(*) FROM applied_jobs
                     WHERE applied_jobs.job_id = jobs.id
                     AND applied_jobs.candidate_id = ?) as applied
                ', [$candidateId, $candidateId])
                ->active()
                ->withoutEdited();

            \Log::info("Query untuk user yang login dibuat dengan candidate_id: {$candidateId}");
        } else {
            $query = Job::with('company.user', 'job_type:id,name')
                ->select('jobs.*')
                ->selectRaw('
                    (SELECT COUNT(*) FROM bookmark_candidate_job
                     WHERE bookmark_candidate_job.job_id = jobs.id) as bookmark_jobs_count,
                    (SELECT COUNT(*) FROM applied_jobs
                     WHERE applied_jobs.job_id = jobs.id) as applied_jobs_count,
                    0 as bookmarked,
                    0 as applied
                ')
                ->withoutEdited()
                ->active();
        }

        // company search
        if ($request->has('company') && $request->company != null) {
            $company = $request->company;
            $query->whereHas('company.user', function ($q) use ($company) {
                $q->where('username', $company);
            });
        }

        // Keyword search
        if ($request->has('keyword') && $request->keyword != null) {
            $query->where('title', 'LIKE', "%$request->keyword%");
        }

        // Category filter
        \Log::info("Mencari kategori dengan slug: {$slug}");

        $id = JobCategory::where('slug', $slug)->first();
        if ($id == null) {
            \Log::error("Kategori dengan slug {$slug} tidak ditemukan");
            // Tidak perlu abort(404) karena sudah ditangani di controller
            return [
                'total_jobs' => 0,
                'jobs' => collect([]),
                'featured_jobs' => collect([]),
                'countries' => Country::all(['id', 'name', 'slug']),
                'categories' => JobCategory::all()->sortBy('name'),
                'job_roles' => JobRole::all()->sortBy('name'),
                'max_salary' => \DB::table('jobs')->max('max_salary'),
                'min_salary' => \DB::table('jobs')->max('min_salary'),
                'experiences' => Experience::all(),
                'educations' => Education::all(),
                'job_types' => JobType::all(),
                'skills' => Skill::all()->sortBy('name'),
                'popularTags' => $this->popularTags(),
            ];
        } else {
            $category_id = $id->id;
            \Log::info("Kategori ditemukan dengan ID: {$category_id}");
            $query->where('category_id', $category_id);
        }

        // job role filter
        if ($request->has('job_role') && $request->job_role != null) {
            $query->where('role_id', $request->job_role);
        }

        // Salary filter
        if ($request->has('gaji_min') && $request->gaji_min != null && $request->gaji_min != '') {
            $query->where('min_salary', '>=', $request->gaji_min);
        } else if ($request->has('price_min') && $request->price_min != null && $request->price_min != '') {
            // For backward compatibility
            $query->where('min_salary', '>=', $request->price_min);
        }

        if ($request->has('gaji_max') && $request->gaji_max != null && $request->gaji_max != '') {
            $query->where('max_salary', '<=', $request->gaji_max);
        } else if ($request->has('price_max') && $request->price_max != null && $request->price_max != '') {
            // For backward compatibility
            $query->where('max_salary', '<=', $request->price_max);
        }

        // tags filter
        if ($request->has('tag') && $request->tag != null) {
            $tag = TagTranslation::where('name', $request->tag)->first();

            if ($tag) {
                $query->whereHas('tags', function ($q) use ($tag) {
                    $q->where('job_tag.tag_id', $tag->tag_id);
                });
            }
        }

        // location
        $final_address = '';
        if ($request->has('location') && $request->location != null) {
            $adress = $request->location;
            if ($adress) {
                $adress_array = explode(' ', $adress);
                if ($adress_array) {
                    $last_two = array_splice($adress_array, -2);
                }
                $final_address = Str::slug(implode(' ', $last_two));
            }
        }

        // Kabupaten/Kota filter
        if ($request->has('kabupaten_kota') && $request->kabupaten_kota != null) {
            $query->where('district', $request->kabupaten_kota);
        }

        // Locality/Kecamatan filter
        if ($request->has('kecamatan') && $request->kecamatan != null) {
            $query->where('locality', $request->kecamatan);
        } elseif ($request->has('locality') && $request->locality != null) {
            // For backward compatibility
            $query->where('locality', $request->locality);
        }
        // lat Long
        if ($request->has('lat') && $request->has('long') && $request->lat != null && $request->long != null) {
            session()->forget('selected_country');
            $query->Where('address', $final_address ? $final_address : '')
                ->orWhere('country', $request->location ? $request->location : '');
        }

        // country
        $selected_country = session()->get('selected_country');

        if ($selected_country && $selected_country != null) {
            $country = selected_country()->name;
            $query->where('country', 'LIKE', "%$country%");
        } else {

            $setting = loadSetting();
            if ($setting->app_country_type == 'single_base') {
                if ($setting->app_country) {

                    $country = Country::where('id', $setting->app_country)->first();
                    if ($country) {
                        $query->where('country', 'LIKE', "%$country->name%");
                    }
                }
            }
        }

        // Sort by ads
        if ($request->has('sort_by') && $request->sort_by != null) {
            switch ($request->sort_by) {
                case 'latest':
                    $query->latest('id');
                    break;
                case 'featured':
                    $query->where('featured', 1)->latest();
                    break;
            }
        }

        // Experience filter
        if ($request->has('experience') && $request->experience != null) {
            $experience_id = Experience::where('name', $request->experience)->value('id');
            $query->where('experience_id', $experience_id);
        }

        // Education filter
        if ($request->has('pendidikan') && $request->pendidikan != null) {
            $education_translation = EducationTranslation::where('name', $request->pendidikan)->first();
            if ($education_translation) {
                $query->where('education_id', $education_translation->education_id);
            }
        } elseif ($request->has('education') && $request->education != null) {
            // For backward compatibility
            $education_translation = EducationTranslation::where('name', $request->education)->first();
            if ($education_translation) {
                $query->where('education_id', $education_translation->education_id);
            }
        }

        // Work type filter
        if ($request->has('is_remote') && $request->is_remote != null) {
            $query->where('is_remote', 1);
        }

        // Job type filter
        if ($request->has('jenis_pekerjaan') && $request->jenis_pekerjaan != null) {
            $job_type_translation = JobTypeTranslation::where('name', $request->jenis_pekerjaan)->first();
            if ($job_type_translation) {
                $query->where('job_type_id', $job_type_translation->job_type_id);
            }
        } elseif ($request->has('job_type') && $request->job_type != null) {
            // For backward compatibility
            $job_type_translation = JobTypeTranslation::where('name', $request->job_type)->first();
            if ($job_type_translation) {
                $query->where('job_type_id', $job_type_translation->job_type_id);
            }
        }

        // Disability friendly filter
        if ($request->has('ramah_disabilitas') && $request->ramah_disabilitas != null) {
            $query->where('disability_friendly', 1);
        } elseif ($request->has('disability_friendly') && $request->disability_friendly != null) {
            // For backward compatibility
            $query->where('disability_friendly', 1);
        }

        $featured_jobs = $query->latest()->where('featured', 1)->take(9)->get();
        \Log::info("Jumlah featured jobs: {$featured_jobs->count()}");

        // Clone query untuk jobs agar tidak terpengaruh oleh featured jobs query
        $jobsQuery = clone $query;
        $jobs = $jobsQuery->latest()->paginate(9)->withQueryString();
        \Log::info("Jumlah jobs: {$jobs->count()}, Total: {$jobs->total()}");

        // Debug SQL query
        \Log::info("SQL Query: " . $jobsQuery->toSql());
        \Log::info("SQL Bindings: " . json_encode($jobsQuery->getBindings()));

        return [
            'total_jobs' => $jobs->total(),
            'jobs' => $jobs,
            'featured_jobs' => $featured_jobs,
            'countries' => Country::all(['id', 'name', 'slug']),
            'categories' => JobCategory::all()->sortBy('name'),
            'job_roles' => JobRole::all()->sortBy('name'),
            'max_salary' => \DB::table('jobs')->max('max_salary'),
            'min_salary' => \DB::table('jobs')->max('min_salary'),
            'experiences' => Experience::all(),
            'educations' => Education::all(),
            'job_types' => JobType::all(),
            'skills' => Skill::all()->sortBy('name'),
            'popularTags' => $this->popularTags(),
        ];
    }

    /**
     * Return job details with related jobs, benefits, education, experience, tags, role, questions, job type and company user.
     *
     * @param  \App\Models\Job  $job
     * @return array
     */
    private function getJobDetails($job)
    {
        if (auth()->user() && auth()->user()->role == 'candidate' && auth()->user()->candidate) {
            $candidateId = auth()->user()->candidate->id;

            // Gunakan query builder untuk mendapatkan status bookmark
            $isBookmarked = DB::table('bookmark_candidate_job')
                ->where('job_id', $job->id)
                ->where('candidate_id', $candidateId)
                ->exists();

            // Gunakan query builder untuk mendapatkan status applied
            $isApplied = DB::table('applied_jobs')
                ->where('job_id', $job->id)
                ->where('candidate_id', $candidateId)
                ->exists();

            // Log untuk debugging
            \Log::info("Job ID: {$job->id}, Candidate ID: {$candidateId}, isBookmarked: " . ($isBookmarked ? 'true' : 'false'));

            // Load job details
            $job_details = $job
                ->load([
                    'benefits',
                    'education',
                    'experience',
                    'tags',
                    'role',
                    'questions',
                    'job_type',
                    'company.user' => function ($q) {
                        return $q->with('contactInfo', 'socialInfo');
                    },
                ])
                ->loadCount([
                    'bookmarkJobs',
                    'appliedJobs as applied_jobs_count',
                ]);

            // Set status bookmark dan applied secara manual
            $job_details->bookmarked = $isBookmarked;
            $job_details->applied = $isApplied;

            // Pastikan nilai bookmarked dikonversi ke boolean
            $job_details->setAttribute('bookmarked', (bool)$isBookmarked);
            $job_details->setAttribute('applied', (bool)$isApplied);
        } else {
            // Load job details
            $job_details = $job
                ->load([
                    'benefits',
                    'education',
                    'experience',
                    'tags',
                    'role',
                    'questions',
                    'job_type',
                    'company.user' => function ($q) {
                        return $q->with('contactInfo', 'socialInfo');
                    },
                ])
                ->loadCount([
                    'bookmarkJobs',
                    'appliedJobs as applied_jobs_count',
                ]);

            // Set status bookmark dan applied secara manual
            $job_details->bookmarked = false;
            $job_details->applied = false;

            // Pastikan nilai bookmarked dikonversi ke boolean
            $job_details->setAttribute('bookmarked', false);
            $job_details->setAttribute('applied', false);
        }

        // Related Jobs With Single && Multiple Country Base
        $related_jobs_query = Job::query()
            ->withoutEdited()
            ->active()
            ->with([
                'benefits',
                'education',
                'experience',
                'tags',
                'role',
                'questions',
                'job_type',
                'company.user' => function ($q) {
                    return $q->with('contactInfo', 'socialInfo');
                },
            ])
            ->where('id', '!=', $job->id)
            ->where('category_id', $job->category_id);

        $setting = loadSetting();
        if ($setting->app_country_type == 'single_base') {
            if ($setting->app_country) {
                $country = Country::where('id', $setting->app_country)->first();
                if ($country) {
                    $related_jobs_query->where('country', 'LIKE', "%$country->name%");
                }
            }
        } else {
            $selected_country = session()->get('selected_country');

            if ($selected_country && $selected_country != null) {
                $country = selected_country()->name;
                $related_jobs_query->where('country', 'LIKE', "%$country%");
            }
        }

        // Get related jobs
        $related_jobs_collection = $related_jobs_query->latest()->limit(10)->get();

        // If user is logged in as candidate, check bookmark status for each job
        if (auth()->user() && auth()->user()->role == 'candidate' && auth()->user()->candidate) {
            $candidateId = auth()->user()->candidate->id;

            // Get all bookmarked job IDs for this candidate
            $bookmarkedJobIds = DB::table('bookmark_candidate_job')
                ->where('candidate_id', $candidateId)
                ->pluck('job_id')
                ->toArray();

            // Log untuk debugging
            \Log::info("Related Jobs - Candidate ID: {$candidateId}, Bookmarked Job IDs: " . json_encode($bookmarkedJobIds));

            // Set bookmarked status for each job
            foreach ($related_jobs_collection as $related_job) {
                $isBookmarked = in_array($related_job->id, $bookmarkedJobIds);
                $related_job->bookmarked = $isBookmarked;

                // Pastikan nilai bookmarked dikonversi ke boolean dan disimpan sebagai atribut
                $related_job->setAttribute('bookmarked', (bool)$isBookmarked);

                // Log untuk debugging
                \Log::info("Related Job ID: {$related_job->id}, isBookmarked: " . ($isBookmarked ? 'true' : 'false'));
            }
        } else {
            // Set bookmarked status to false for all jobs
            foreach ($related_jobs_collection as $related_job) {
                $related_job->bookmarked = false;

                // Pastikan nilai bookmarked dikonversi ke boolean dan disimpan sebagai atribut
                $related_job->setAttribute('bookmarked', false);
            }
        }

        $related_jobs = $related_jobs_collection;

        if (auth('user')->check() && authUser()->role == 'candidate') {
            $resumes = currentCandidate()->resumes;
        } else {
            $resumes = [];
        }

        return [
            'job' => $job_details,
            'related_jobs' => $related_jobs,
            'resumes' => $resumes,
        ];
    }



    public function jobTagsInsert($tags, $job)
    {
        if ($tags) {
            $tagsArray = [];

            foreach ($tags as $tag) {
                $tag_exists = TagTranslation::where('name', $tag)->first();

                if ($tag_exists) {
                    $taggable = TagTranslation::where('tag_id', $tag)
                        ->orWhere('name', $tag)
                        ->exists();

                    if (! $taggable) {
                        $new_tag = Tag::create(['name' => $tag]);

                        $languages = loadLanguage();
                        foreach ($languages as $language) {
                            $new_tag->translateOrNew($language->code)->name = $tag;
                        }
                        $new_tag->save();

                        array_push($tagsArray, $new_tag->id);
                    } else {
                        array_push($tagsArray, $tag);
                    }
                }
            }

            $job->tags()->attach($tagsArray);
        }
    }

    public function jobTagsSync($tags, $job)
    {
        if ($tags) {
            $tagsArray = [];

            foreach ($tags as $tag) {
                $tag_exists = TagTranslation::where('name', $tag)->first();

                if ($tag_exists) {
                    $taggable = TagTranslation::where('tag_id', $tag)
                        ->orWhere('name', $tag)
                        ->first();

                    if (! $taggable) {
                        $new_tag = Tag::create(['name' => $tag]);

                        $languages = loadLanguage();
                        foreach ($languages as $language) {
                            $new_tag->translateOrNew($language->code)->name = $tag;
                        }
                        $new_tag->save();

                        array_push($tagsArray, $new_tag->id);
                    } else {
                        array_push($tagsArray, $tag);
                    }
                }
            }

            $job->tags()->sync($tagsArray);
        }
    }

    public function jobSkillsInsert($skills, $job)
    {
        if ($skills) {
            $skillsArray = [];

            foreach ($skills as $skill) {
                $skill_exists = SkillTranslation::where('skill_id', $skill)
                    ->orWhere('name', $skill)
                    ->first();

                if (! $skill_exists) {
                    $select_skill = Skill::create(['name' => $skill]);

                    $languages = loadLanguage();
                    foreach ($languages as $language) {
                        $select_skill->translateOrNew($language->code)->name = $skill;
                    }
                    $select_skill->save();

                    array_push($skillsArray, $select_skill->id);
                } else {
                    array_push($skillsArray, $skill_exists->skill_id);
                }
            }

            $job->skills()->attach($skillsArray);
        }
    }

    public function jobSkillsSync($skills, $job)
    {
        if ($skills) {
            $skillsArray = [];

            foreach ($skills as $skill) {
                $skill_exists = SkillTranslation::where('skill_id', $skill)
                    ->orWhere('name', $skill)
                    ->first();

                if (! $skill_exists) {
                    $select_skill = Skill::create(['name' => $skill]);

                    $languages = loadLanguage();
                    foreach ($languages as $language) {
                        $select_skill->translateOrNew($language->code)->name = $skill;
                    }
                    $select_skill->save();

                    array_push($skillsArray, $select_skill->id);
                } else {
                    array_push($skillsArray, $skill_exists->skill_id);
                }
            }

            $job->skills()->sync($skillsArray);
        }
    }

    public function jobBenefitsInsert($benefits, $job)
    {
        if ($benefits && count($benefits)) {
            $benefitsArray = [];

            foreach ($benefits as $benefit) {
                // Cek apakah benefit adalah ID atau nama baru
                if (is_numeric($benefit)) {
                    // Jika numeric, anggap sebagai ID yang sudah ada
                    array_push($benefitsArray, $benefit);
                } else {
                    // Jika string, cek apakah benefit sudah ada atau buat baru
                    $benefit_exists = BenefitTranslation::where('name', $benefit)->first();

                    if (!$benefit_exists) {
                        // Buat benefit baru
                        $new_benefit = new Benefit();
                        $new_benefit->company_id = currentCompany() ? currentCompany()->id : null;
                        $new_benefit->save();

                        $languages = loadLanguage();
                        foreach ($languages as $language) {
                            $new_benefit->translateOrNew($language->code)->name = $benefit;
                        }
                        $new_benefit->save();

                        array_push($benefitsArray, $new_benefit->id);
                    } else {
                        array_push($benefitsArray, $benefit_exists->benefit_id);
                    }
                }
            }

            $job->benefits()->attach($benefitsArray);
        }
    }

    public function jobBenefitsSync($benefits, $job)
    {
        if ($benefits && count($benefits)) {
            $benefitsArray = [];

            foreach ($benefits as $benefit) {
                // Cek apakah benefit adalah ID atau nama baru
                if (is_numeric($benefit)) {
                    // Jika numeric, anggap sebagai ID yang sudah ada
                    array_push($benefitsArray, $benefit);
                } else {
                    // Jika string, cek apakah benefit sudah ada atau buat baru
                    $benefit_exists = BenefitTranslation::where('name', $benefit)->first();

                    if (!$benefit_exists) {
                        // Buat benefit baru
                        $new_benefit = new Benefit();
                        $new_benefit->company_id = currentCompany() ? currentCompany()->id : null;
                        $new_benefit->save();

                        $languages = loadLanguage();
                        foreach ($languages as $language) {
                            $new_benefit->translateOrNew($language->code)->name = $benefit;
                        }
                        $new_benefit->save();

                        array_push($benefitsArray, $new_benefit->id);
                    } else {
                        array_push($benefitsArray, $benefit_exists->benefit_id);
                    }
                }
            }

            $job->benefits()->sync($benefitsArray);
        } else {
            // Jika tidak ada benefits, hapus semua relasi
            $job->benefits()->sync([]);
        }
    }

    public function popularTags()
    {
        return Tag::with('translations')
            ->popular()
            ->withCount('tags')
            ->latest('tags_count')
            ->get()
            ->take(10);
    }

    /**
     * Toggle bookmark job
     *
     * @return void
     */
    public function toggleBookmarkJob(Job $job)
    {
        try {
            // Pastikan candidate ada
            if (!currentCandidate()) {
                if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Profil pencaker tidak ditemukan',
                        'bookmarked' => false
                    ], 400);
                }

                flashError('Profil pencaker tidak ditemukan');
                return back();
            }

            // Toggle bookmark
            $check = $job->bookmarkJobs()->toggle(currentCandidate());

            // Periksa status bookmark setelah toggle
            $isBookmarked = !empty($check['attached']);

            // Log untuk debugging
            \Log::info("toggleBookmarkJob - Job ID: {$job->id}, Candidate ID: " . currentCandidate()->id . ", isBookmarked: " . ($isBookmarked ? 'true' : 'false'));

            // Update model attribute untuk memastikan nilai bookmarked diperbarui
            $job->bookmarked = $isBookmarked;
            $job->setAttribute('bookmarked', $isBookmarked);

            if ($isBookmarked) {
                $user = authUser();
                // make notification to company candidate bookmark job
                Notification::send($job->company->user, new BookmarkJobNotification($user, $job));
            }

            $message = $isBookmarked ? __('Loker ditambahkan ke daftar favorit!') : __('Loker dihapus dari daftar favorit!');

            // Check if request is AJAX
            if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'bookmarked' => $isBookmarked,
                    'job_id' => $job->id
                ]);
            }

            flashSuccess($message);
            return back();
        } catch (\Exception $e) {
            \Log::error("Error toggleBookmarkJob - " . $e->getMessage());

            if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                    'bookmarked' => false
                ], 500);
            }

            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Toggle apply job
     *
     * @param  Candidate  $candidate
     * @return void
     */
    public function toggleApplyJob(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'resume_id' => 'required',
                'cover_letter' => 'required',
            ],
            [
                'resume_id.required' => 'Harap pilih CV/Resume.',
                'cover_letter.required' => 'Harap masukkan cover surat.',
            ],
        );

        if ($validator->fails()) {
            flashError($validator->errors()->first());

            return back();
        }

        // Cek kelengkapan profil pencaker
        $candidate = currentCandidate();
        $profileIncomplete = false;
        $missingFields = [];

        // Cek field wajib yang harus diisi
        if (!$candidate->user->name) {
            $missingFields[] = 'Nama lengkap';
            $profileIncomplete = true;
        }

        if (!$candidate->user->contactInfo || !$candidate->user->contactInfo->phone) {
            $missingFields[] = 'Nomor telepon';
            $profileIncomplete = true;
        }

        if (!$candidate->birth_date) {
            $missingFields[] = 'Tanggal lahir';
            $profileIncomplete = true;
        }

        if (!$candidate->gender) {
            $missingFields[] = 'Jenis kelamin';
            $profileIncomplete = true;
        }

        if (!$candidate->marital_status) {
            $missingFields[] = 'Status perkawinan';
            $profileIncomplete = true;
        }

        if (!$candidate->profession_id) {
            $missingFields[] = 'Profesi';
            $profileIncomplete = true;
        }

        // Cek apakah ada CV/Resume
        if ($candidate->resumes->count() == 0) {
            $missingFields[] = 'CV/Resume';
            $profileIncomplete = true;
        }

        if ($profileIncomplete) {
            $message = 'Lengkapi profil Anda sebelum melamar pekerjaan. Field yang belum diisi: ' . implode(', ', $missingFields);
            flashError($message);
            return redirect()->route('candidate.dashboard');
        }

        $candidate = currentCandidate();
        $job = Job::find($request->id);

        // Get default application group for 'Semua Lamaran'
        $default_group = \App\Models\DefaultApplicationGroup::where('name', 'Semua Lamaran')->first();

        // Get company application group for 'Semua Lamaran'
        $company_group = $job->company->applicationGroups->where('is_deleteable', false)->first();

        DB::table('applied_jobs')->insert([
            'candidate_id' => $candidate->id,
            'job_id' => $job->id,
            'cover_letter' => $request->cover_letter,
            'candidate_resume_id' => $request->resume_id,
            'application_group_id' => $company_group->id ?? 1,
            'default_application_group_id' => $default_group->id ?? null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // make notification to candidate and company for notify
        $job->company->user->notify(new ApplyJobNotification(authUser(), $job->company->user, $job));

        if (authUser()->recent_activities_alert) {
            auth('user')
                ->user()
                ->notify(new ApplyJobNotification(authUser(), $job->company->user, $job));
        }

        flashSuccess(__('job_applied_successfully'));

        return back();
    }

    /**
     * job benefit create
     *
     * @return Renderable
     */
    public function jobBenefitCreate(Request $request)
    {
        $benefit = $request->benefit;
        $languages = loadLanguage();
        $benefit_exists = BenefitTranslation::where('name', $benefit)->first();

        if ($benefit_exists) {
            return __('benefit_already_exists');
        }

        $translation = new Benefit();
        $translation->company_id = currentCompany()->id;
        $translation->save();

        foreach ($languages as $language) {
            $translation->translateOrNew($language->code)->name = $benefit;
        }

        $translation->save();

        return $translation;
    }
}
