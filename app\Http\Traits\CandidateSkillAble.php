<?php

namespace App\Http\Traits;

use App\Models\CandidateEducation;
use App\Models\CandidateExperience;
use Illuminate\Http\Request;

trait CandidateSkillAble
{
    public function experienceStore(Request $request)
    {
        $request->session()->put('type', 'experience');

        $candidate = currentCandidate();

        // Cek apakah sudah ada fresh graduate experience
        $hasFreshGraduate = CandidateExperience::where('candidate_id', $candidate->id)
            ->where('fresh_graduate', 1)
            ->exists();

        // Cek apakah sudah ada pengalaman kerja
        $hasWorkExperience = CandidateExperience::where('candidate_id', $candidate->id)
            ->where('fresh_graduate', 0)
            ->exists();

        // Jika fresh graduate, tidak perlu validasi field pengalaman
        if ($request->fresh_graduate) {
            // Tidak boleh menambah fresh graduate jika sudah ada pengalaman kerja
            if ($hasWorkExperience) {
                return back()->withErrors(['fresh_graduate' => 'Tidak dapat menambah status Fresh Graduate karena sudah memiliki pengalaman kerja. Hapus pengalaman kerja terlebih dahulu.']);
            }

            // Tidak boleh menambah fresh graduate jika sudah ada
            if ($hasFreshGraduate) {
                return back()->withErrors(['fresh_graduate' => 'Status Fresh Graduate sudah ada.']);
            }

            $request->validate([
                'fresh_graduate' => 'required|boolean',
            ]);
        } else {
            // Tidak boleh menambah pengalaman kerja jika sudah ada fresh graduate
            if ($hasFreshGraduate) {
                return back()->withErrors(['company' => 'Tidak dapat menambah pengalaman kerja karena status Fresh Graduate sudah ada. Hapus status Fresh Graduate terlebih dahulu.']);
            }

            $request->validate([
                'company' => 'required',
                'department' => 'required',
                'designation' => 'required',
                'start' => 'required',
                'end' => 'sometimes',
            ]);
        }

        $start_date = $request->start ? formatTime($request->start, 'Y-m-d') : null;
        $end_date = $request->end ? formatTime($request->end, 'Y-m-d') : null;

        CandidateExperience::create([
            'candidate_id' => $candidate->id,
            'company' => $request->fresh_graduate ? null : $request->company,
            'department' => $request->fresh_graduate ? null : $request->department,
            'designation' => $request->fresh_graduate ? null : $request->designation,
            'start' => $request->fresh_graduate ? null : $start_date,
            'end' => $request->fresh_graduate ? null : $end_date,
            'responsibilities' => $request->fresh_graduate ? null : $request->responsibilities,
            'currently_working' => $request->fresh_graduate ? 0 : ($request->currently_working ?? 0),
            'fresh_graduate' => $request->fresh_graduate ?? 0,
        ]);

        return back()->with('success', 'Experience added successfully');
    }

    public function experienceUpdate(Request $request)
    {
        $request->session()->put('type', 'experience');

        $candidate = currentCandidate();
        $experience = CandidateExperience::findOrFail($request->experience_id);

        // Cek apakah sudah ada fresh graduate experience (selain yang sedang diedit)
        $hasFreshGraduate = CandidateExperience::where('candidate_id', $candidate->id)
            ->where('fresh_graduate', 1)
            ->where('id', '!=', $experience->id)
            ->exists();

        // Cek apakah sudah ada pengalaman kerja (selain yang sedang diedit)
        $hasWorkExperience = CandidateExperience::where('candidate_id', $candidate->id)
            ->where('fresh_graduate', 0)
            ->where('id', '!=', $experience->id)
            ->exists();

        // Jika fresh graduate, tidak perlu validasi field pengalaman
        if ($request->fresh_graduate) {
            // Tidak boleh mengubah ke fresh graduate jika sudah ada pengalaman kerja lain
            if ($hasWorkExperience) {
                return back()->withErrors(['fresh_graduate' => 'Tidak dapat mengubah ke status Fresh Graduate karena sudah memiliki pengalaman kerja lain. Hapus pengalaman kerja lain terlebih dahulu.']);
            }

            // Tidak boleh mengubah ke fresh graduate jika sudah ada fresh graduate lain
            if ($hasFreshGraduate) {
                return back()->withErrors(['fresh_graduate' => 'Status Fresh Graduate sudah ada di pengalaman lain.']);
            }

            $request->validate([
                'fresh_graduate' => 'required|boolean',
            ]);
        } else {
            // Tidak boleh mengubah ke pengalaman kerja jika sudah ada fresh graduate
            if ($hasFreshGraduate) {
                return back()->withErrors(['company' => 'Tidak dapat mengubah ke pengalaman kerja karena status Fresh Graduate sudah ada. Hapus status Fresh Graduate terlebih dahulu.']);
            }

            $request->validate([
                'company' => 'required',
                'designation' => 'required',
                'department' => 'required',
                'start' => 'required',
                'end' => 'sometimes',
            ]);
        }

        $start_date = $request->start ? formatTime($request->start, 'Y-m-d') : null;
        $end_date = $request->end ? formatTime($request->end, 'Y-m-d') : null;

        $experience->update([
            'candidate_id' => $candidate->id,
            'company' => $request->fresh_graduate ? null : $request->company,
            'department' => $request->fresh_graduate ? null : $request->department,
            'designation' => $request->fresh_graduate ? null : $request->designation,
            'start' => $request->fresh_graduate ? null : $start_date,
            'end' => $request->fresh_graduate ? null : $end_date,
            'responsibilities' => $request->fresh_graduate ? null : $request->responsibilities,
            'currently_working' => $request->fresh_graduate ? 0 : ($request->currently_working ?? 0),
            'fresh_graduate' => $request->fresh_graduate ?? 0,
        ]);

        return back()->with('success', 'Experience updated successfully');
    }

    public function experienceDelete(CandidateExperience $experience)
    {
        session()->put('type', 'experience');

        $experience->delete();

        return back()->with('success', 'Experience deleted successfully');
    }

    public function educationStore(Request $request)
    {
        $request->session()->put('type', 'experience');

        $request->validate([
            'level' => 'required',
            'degree' => 'required',
            'year' => 'required',
        ]);

        CandidateEducation::create([
            'candidate_id' => currentCandidate()->id,
            'level' => $request->level,
            'degree' => $request->degree,
            'year' => $request->year,
            'notes' => $request->notes,
        ]);

        return back()->with('success', 'Education added successfully');
    }

    public function educationUpdate(Request $request)
    {
        $request->session()->put('type', 'experience');

        $request->validate([
            'level' => 'required',
            'degree' => 'required',
            'year' => 'required',
        ]);

        $education = CandidateEducation::findOrFail($request->education_id);

        $education->update([
            'candidate_id' => currentCandidate()->id,
            'level' => $request->level,
            'degree' => $request->degree,
            'year' => $request->year,
            'notes' => $request->notes,
        ]);

        return back()->with('success', 'Education updated successfully');
    }

    public function educationDelete(CandidateEducation $education)
    {
        session()->put('type', 'experience');

        $education->delete();

        return back()->with('success', 'Education deleted successfully');
    }
}
