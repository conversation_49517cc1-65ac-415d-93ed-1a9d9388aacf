<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SendProfileVerifiedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Profil Berhasil Diverifikasi')
            ->line('Yth, '.$notifiable->name.',')
            ->line('Kami dengan senang hati memberitahukan Anda bahwa profil Anda di '.env('APP_NAME').' telah berhasil diverifikasi.')
            ->line('Dengan profil terverifikasi, kini Anda dapat memanfaatkan sepenuhnya fitur dan peluang yang ditingkatkan di platform kami.')
            ->line('Terima kasih atas kerja sama Anda selama proses verifikasi. Kami berharap Anda mendapatkan hasil terbaik dalam pencarian  Anda.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'title' => __('Perusahaan Anda di '.env('APP_NAME').' berhasil diverifikasi.'),
            'url' => route('company.dashboard'),
        ];
    }
}
