<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;
use App\Services\LogService;

class DatabaseToolsController extends Controller
{
    /**
     * Display the database tools dashboard
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        abort_if(!userCan('setting.view'), 403);

        // Get list of backups
        $backups = $this->getBackups();

        // Get cloud storage settings
        $cloudSettings = $this->getCloudSettings();

        // Get backup schedule settings
        $backupSchedule = $this->getBackupSchedule();

        return view('backend.settings.pages.database-tools.index', compact('backups', 'cloudSettings', 'backupSchedule'));
    }

    /**
     * Get list of backups
     *
     * @return array
     */
    private function getBackups()
    {
        $backups = [];
        $backupPath = storage_path('app/laravel-backup');

        // Create backup directory if it doesn't exist
        if (!File::exists($backupPath)) {
            File::makeDirectory($backupPath, 0755, true);
        }

        $files = File::files($backupPath);

        foreach ($files as $file) {
            // Only include zip files
            if (pathinfo($file->getFilename(), PATHINFO_EXTENSION) === 'zip') {
                $backups[] = [
                    'name' => $file->getFilename(),
                    'size' => $this->formatBytes($file->getSize()),
                    'date' => Carbon::createFromTimestamp($file->getMTime())->format('d M Y H:i:s'),
                    'path' => $file->getPathname(),
                ];
            }
        }

        // Sort by date (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return $backups;
    }

    /**
     * Get cloud storage settings
     *
     * @return array
     */
    private function getCloudSettings()
    {
        return [
            'google_drive' => [
                'enabled' => config('backup.cloud.google_drive.enabled', false),
                'client_id' => config('backup.cloud.google_drive.client_id', ''),
                'client_secret' => config('backup.cloud.google_drive.client_secret', ''),
                'refresh_token' => config('backup.cloud.google_drive.refresh_token', ''),
                'folder_id' => config('backup.cloud.google_drive.folder_id', ''),
            ],
            'amazon_s3' => [
                'enabled' => config('backup.cloud.amazon_s3.enabled', false),
                'key' => config('backup.cloud.amazon_s3.key', ''),
                'secret' => config('backup.cloud.amazon_s3.secret', ''),
                'region' => config('backup.cloud.amazon_s3.region', ''),
                'bucket' => config('backup.cloud.amazon_s3.bucket', ''),
            ],
            'backblaze' => [
                'enabled' => config('backup.cloud.backblaze.enabled', false),
                'key_id' => config('backup.cloud.backblaze.key_id', ''),
                'application_key' => config('backup.cloud.backblaze.application_key', ''),
                'bucket' => config('backup.cloud.backblaze.bucket', ''),
            ],
        ];
    }

    /**
     * Get backup schedule settings
     *
     * @return array
     */
    private function getBackupSchedule()
    {
        return [
            'enabled' => config('backup.schedule.enabled', false),
            'frequency' => config('backup.schedule.frequency', 'daily'),
            'time' => config('backup.schedule.time', '00:00'),
            'keep_days' => config('backup.schedule.keep_days', 7),
        ];
    }

    /**
     * Create a new backup
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function createBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Determine backup type
            $backupType = $request->input('backup_type', 'db');

            // Create backup name with timestamp
            $timestamp = now()->format('Y-m-d-H-i-s');
            $backupName = "backup-{$timestamp}";

            // Ensure backup directory exists
            $backupPath = storage_path('app/laravel-backup');
            if (!File::exists($backupPath)) {
                File::makeDirectory($backupPath, 0755, true);
            }

            if ($backupType === 'db') {
                // Database only backup using Spatie DB Dumper
                $databaseName = config('database.connections.mysql.database');
                $username = config('database.connections.mysql.username');
                $password = config('database.connections.mysql.password');
                $host = config('database.connections.mysql.host');
                $port = config('database.connections.mysql.port', 3306);

                // Create dump file
                $dumpFile = storage_path("app/laravel-backup/{$backupName}.sql");

                // Use PHP's PDO to create a database dump
                try {
                    // Connect to the database
                    $dsn = "mysql:host={$host};port={$port};dbname={$databaseName}";
                    $pdo = new \PDO($dsn, $username, $password);
                    $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

                    // Get all tables
                    $tables = [];
                    $result = $pdo->query('SHOW TABLES');
                    while ($row = $result->fetch(\PDO::FETCH_NUM)) {
                        $tables[] = $row[0];
                    }

                    // Start the dump content
                    $dump = "-- Database dump created on " . date('Y-m-d H:i:s') . "\n";
                    $dump .= "-- Server: {$host}\n";
                    $dump .= "-- Database: {$databaseName}\n\n";
                    $dump .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

                    // Process each table
                    foreach ($tables as $table) {
                        // Get create table statement
                        $stmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
                        $row = $stmt->fetch(\PDO::FETCH_NUM);
                        $dump .= "DROP TABLE IF EXISTS `{$table}`;\n";
                        $dump .= $row[1] . ";\n\n";

                        // Get count of rows in table
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
                        $rowCount = $countStmt->fetchColumn();

                        if ($rowCount > 0) {
                            // Get column names
                            $columnStmt = $pdo->query("SHOW COLUMNS FROM `{$table}`");
                            $columns = [];
                            while ($column = $columnStmt->fetch(\PDO::FETCH_ASSOC)) {
                                $columns[] = $column['Field'];
                            }

                            // Process in batches to avoid memory issues
                            $batchSize = 1000;
                            $batches = ceil($rowCount / $batchSize);

                            for ($batch = 0; $batch < $batches; $batch++) {
                                $offset = $batch * $batchSize;
                                $stmt = $pdo->query("SELECT * FROM `{$table}` LIMIT {$batchSize} OFFSET {$offset}");
                                $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);

                                if (count($rows) > 0) {
                                    $dump .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

                                    $values = [];
                                    foreach ($rows as $row) {
                                        $rowValues = [];
                                        foreach ($columns as $column) {
                                            $value = $row[$column] ?? null;
                                            if ($value === null) {
                                                $rowValues[] = 'NULL';
                                            } else {
                                                $rowValues[] = $pdo->quote($value);
                                            }
                                        }
                                        $values[] = "(" . implode(', ', $rowValues) . ")";
                                    }

                                    $dump .= implode(",\n", $values) . ";\n\n";
                                }

                                // Free up memory
                                unset($rows);
                                unset($values);
                                gc_collect_cycles();
                            }
                        }
                    }

                    $dump .= "SET FOREIGN_KEY_CHECKS=1;\n";

                    // Write the dump to file
                    File::put($dumpFile, $dump);

                    // Create zip file
                    $zipFile = storage_path("app/laravel-backup/{$backupName}.zip");
                    $zip = new \ZipArchive();
                    if ($zip->open($zipFile, \ZipArchive::CREATE) === TRUE) {
                        $zip->addFile($dumpFile, basename($dumpFile));
                        $zip->close();

                        // Remove the SQL file
                        File::delete($dumpFile);
                    } else {
                        throw new \Exception('Gagal membuat file zip');
                    }
                } catch (\Exception $e) {
                    Log::error('Database dump error: ' . $e->getMessage());
                    throw new \Exception('Backup database gagal: ' . $e->getMessage());
                }
            } else {
                // Full backup (database + files)
                try {
                    // Get database connection details
                    $databaseName = config('database.connections.mysql.database');
                    $username = config('database.connections.mysql.username');
                    $password = config('database.connections.mysql.password');
                    $host = config('database.connections.mysql.host');
                    $port = config('database.connections.mysql.port', 3306);

                    // First create database dump using PDO
                    $dsn = "mysql:host={$host};port={$port};dbname={$databaseName}";
                    $pdo = new \PDO($dsn, $username, $password);
                    $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

                    // Get all tables
                    $tables = [];
                    $result = $pdo->query('SHOW TABLES');
                    while ($row = $result->fetch(\PDO::FETCH_NUM)) {
                        $tables[] = $row[0];
                    }

                    // Start the dump content
                    $dump = "-- Database dump created on " . date('Y-m-d H:i:s') . "\n";
                    $dump .= "-- Server: {$host}\n";
                    $dump .= "-- Database: {$databaseName}\n\n";
                    $dump .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

                    // Process each table
                    foreach ($tables as $table) {
                        // Get create table statement
                        $stmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
                        $row = $stmt->fetch(\PDO::FETCH_NUM);
                        $dump .= "DROP TABLE IF EXISTS `{$table}`;\n";
                        $dump .= $row[1] . ";\n\n";

                        // Get count of rows in table
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
                        $rowCount = $countStmt->fetchColumn();

                        if ($rowCount > 0) {
                            // Get column names
                            $columnStmt = $pdo->query("SHOW COLUMNS FROM `{$table}`");
                            $columns = [];
                            while ($column = $columnStmt->fetch(\PDO::FETCH_ASSOC)) {
                                $columns[] = $column['Field'];
                            }

                            // Process in batches to avoid memory issues
                            $batchSize = 1000;
                            $batches = ceil($rowCount / $batchSize);

                            for ($batch = 0; $batch < $batches; $batch++) {
                                $offset = $batch * $batchSize;
                                $stmt = $pdo->query("SELECT * FROM `{$table}` LIMIT {$batchSize} OFFSET {$offset}");
                                $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);

                                if (count($rows) > 0) {
                                    $dump .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

                                    $values = [];
                                    foreach ($rows as $row) {
                                        $rowValues = [];
                                        foreach ($columns as $column) {
                                            $value = $row[$column] ?? null;
                                            if ($value === null) {
                                                $rowValues[] = 'NULL';
                                            } else {
                                                $rowValues[] = $pdo->quote($value);
                                            }
                                        }
                                        $values[] = "(" . implode(', ', $rowValues) . ")";
                                    }

                                    $dump .= implode(",\n", $values) . ";\n\n";
                                }

                                // Free up memory
                                unset($rows);
                                unset($values);
                                gc_collect_cycles();
                            }
                        }
                    }

                    $dump .= "SET FOREIGN_KEY_CHECKS=1;\n";

                    // Write the dump to file
                    $dumpFile = storage_path("app/laravel-backup/{$backupName}.sql");
                    File::put($dumpFile, $dump);

                    // Create zip file for full backup
                    $zipFile = storage_path("app/laravel-backup/{$backupName}-full.zip");
                    $zip = new \ZipArchive();
                    if ($zip->open($zipFile, \ZipArchive::CREATE) === TRUE) {
                        // Add SQL file
                        $zip->addFile($dumpFile, basename($dumpFile));

                        // Add important directories
                        $this->addDirectoryToZip($zip, base_path('app'), 'app');
                        $this->addDirectoryToZip($zip, base_path('config'), 'config');
                        $this->addDirectoryToZip($zip, base_path('resources'), 'resources');
                        $this->addDirectoryToZip($zip, base_path('routes'), 'routes');
                        $this->addDirectoryToZip($zip, storage_path('app/public'), 'storage/app/public');

                        $zip->close();

                        // Remove the SQL file
                        File::delete($dumpFile);
                    } else {
                        throw new \Exception('Gagal membuat file zip');
                    }
                } catch (\Exception $e) {
                    Log::error('Full backup error: ' . $e->getMessage());
                    throw new \Exception('Backup penuh gagal: ' . $e->getMessage());
                }
            }

            // Log admin activity
            LogService::recordAdminActivity('backup', 'database', 'Created a new ' . ($backupType === 'db' ? 'database' : 'full') . ' backup');

            return redirect()->back()
                ->with('success', 'Backup berhasil dibuat!');
        } catch (\Exception $e) {
            Log::error('Backup error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Backup gagal: ' . $e->getMessage());
        }
    }

    /**
     * Add directory to zip file
     *
     * @param \ZipArchive $zip
     * @param string $sourcePath
     * @param string $zipPath
     * @return void
     */
    private function addDirectoryToZip($zip, $sourcePath, $zipPath)
    {
        if (!File::exists($sourcePath)) {
            return;
        }

        // Get all files in the directory
        $files = File::allFiles($sourcePath);
        $totalFiles = count($files);
        $addedFiles = 0;

        // Set maximum file size to add (10MB)
        $maxFileSize = 10 * 1024 * 1024;

        // Add files to zip
        foreach ($files as $file) {
            try {
                // Skip files larger than max size
                if ($file->getSize() > $maxFileSize) {
                    Log::info("Skipping large file: {$file->getPathname()} ({$this->formatBytes($file->getSize())})");
                    continue;
                }

                // Skip files in vendor directory to avoid memory issues
                if (strpos($file->getPathname(), 'vendor') !== false) {
                    continue;
                }

                // Skip files in node_modules directory
                if (strpos($file->getPathname(), 'node_modules') !== false) {
                    continue;
                }

                // Skip log files
                if (pathinfo($file->getPathname(), PATHINFO_EXTENSION) === 'log') {
                    continue;
                }

                $relativePath = $zipPath . '/' . $file->getRelativePathname();
                $zip->addFile($file->getPathname(), $relativePath);
                $addedFiles++;

                // Free up memory
                if ($addedFiles % 100 === 0) {
                    gc_collect_cycles();
                }
            } catch (\Exception $e) {
                Log::warning("Failed to add file to zip: {$file->getPathname()} - {$e->getMessage()}");
            }
        }

        Log::info("Added {$addedFiles} of {$totalFiles} files from {$sourcePath} to backup");
    }

    /**
     * Download a backup
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadBackup(Request $request)
    {
        abort_if(!userCan('setting.view'), 403);

        $backupPath = $request->input('path');

        if (!File::exists($backupPath)) {
            return redirect()->back()
                ->with('error', 'File backup tidak ditemukan!');
        }

        // Log admin activity
        LogService::recordAdminActivity('download', 'database', 'Downloaded a backup file: ' . basename($backupPath));

        return response()->download($backupPath);
    }

    /**
     * Delete a backup
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        $backupPath = $request->input('path');

        if (!File::exists($backupPath)) {
            return redirect()->back()
                ->with('error', 'File backup tidak ditemukan!');
        }

        // Delete the file
        File::delete($backupPath);

        // Log admin activity
        LogService::recordAdminActivity('delete', 'database', 'Deleted a backup file: ' . basename($backupPath));

        return redirect()->back()
            ->with('success', 'Backup berhasil dihapus!');
    }

    /**
     * Restore a backup
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restoreBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        // Validate confirmation
        $request->validate([
            'confirmation' => 'required|in:RESTORE',
            'accept_risks' => 'required|accepted',
        ]);

        $backupPath = $request->input('path');

        if (!File::exists($backupPath)) {
            return redirect()->back()
                ->with('error', 'File backup tidak ditemukan!');
        }

        try {
            // Create a temporary directory for extraction
            $tempDir = storage_path('app/temp-restore-' . time());
            if (!File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            // Extract the backup file
            $zip = new \ZipArchive();
            if ($zip->open($backupPath) === TRUE) {
                $zip->extractTo($tempDir);
                $zip->close();

                // Look for SQL files
                $sqlFiles = File::glob($tempDir . '/*.sql');

                if (count($sqlFiles) > 0) {
                    // Get database connection details
                    $databaseName = config('database.connections.mysql.database');
                    $username = config('database.connections.mysql.username');
                    $password = config('database.connections.mysql.password');
                    $host = config('database.connections.mysql.host');
                    $port = config('database.connections.mysql.port', 3306);

                    // Import the SQL file using mysql command
                    $sqlFile = $sqlFiles[0]; // Use the first SQL file found

                    // Use mysql command to import
                    $command = "mysql --host={$host} --port={$port} --user={$username} --password='{$password}' {$databaseName} < {$sqlFile}";
                    exec($command, $output, $returnVar);

                    if ($returnVar !== 0) {
                        throw new \Exception('Database restore failed: ' . implode("\n", $output));
                    }
                } else {
                    throw new \Exception('Tidak ada file SQL ditemukan dalam backup');
                }

                // Clean up
                File::deleteDirectory($tempDir);

                // Log admin activity
                LogService::recordAdminActivity('restore', 'database', 'Restored from backup: ' . basename($backupPath));

                return redirect()->back()
                    ->with('success', 'Restore berhasil dilakukan!');
            } else {
                throw new \Exception('Gagal membuka file backup');
            }
        } catch (\Exception $e) {
            // Clean up if temp directory exists
            if (isset($tempDir) && File::exists($tempDir)) {
                File::deleteDirectory($tempDir);
            }

            Log::error('Restore error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Restore gagal: ' . $e->getMessage());
        }
    }

    /**
     * Reset database
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetDatabase(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        // Validate confirmation
        $request->validate([
            'confirmation' => 'required|in:HAPUS',
            'accept_risks' => 'required|accepted',
        ]);

        try {
            // Run database migrations fresh with seed
            Artisan::call('migrate:fresh', ['--seed' => true]);

            // Log admin activity
            LogService::recordAdminActivity('reset', 'database', 'Reset database to default state');

            return redirect()->back()
                ->with('success', 'Database berhasil direset ke kondisi awal!');
        } catch (\Exception $e) {
            Log::error('Database reset error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Reset database gagal: ' . $e->getMessage());
        }
    }

    /**
     * Optimize database
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function optimizeDatabase()
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Get all tables
            $tables = DB::select('SHOW TABLES');
            $dbName = 'Tables_in_' . config('database.connections.mysql.database');

            $optimizedTables = 0;
            $errorTables = [];

            // First analyze tables
            foreach ($tables as $table) {
                try {
                    $tableName = $table->$dbName;
                    DB::statement("ANALYZE TABLE `{$tableName}`");
                } catch (\Exception $e) {
                    Log::warning("Failed to analyze table {$tableName}: " . $e->getMessage());
                    $errorTables[] = $tableName;
                }
            }

            // Then optimize tables
            foreach ($tables as $table) {
                try {
                    $tableName = $table->$dbName;
                    DB::statement("OPTIMIZE TABLE `{$tableName}`");
                    $optimizedTables++;
                } catch (\Exception $e) {
                    Log::warning("Failed to optimize table {$tableName}: " . $e->getMessage());
                    if (!in_array($tableName, $errorTables)) {
                        $errorTables[] = $tableName;
                    }
                }
            }

            // Run Laravel's optimize commands
            Artisan::call('optimize:clear');
            Artisan::call('optimize');

            // Log admin activity
            LogService::recordAdminActivity('optimize', 'database', "Optimized {$optimizedTables} database tables");

            if (count($errorTables) > 0) {
                return redirect()->back()
                    ->with('warning', "Database dioptimasi sebagian! {$optimizedTables} tabel berhasil dioptimasi, tetapi " . count($errorTables) . " tabel gagal dioptimasi.");
            }

            return redirect()->back()
                ->with('success', 'Database berhasil dioptimasi!');
        } catch (\Exception $e) {
            Log::error('Database optimization error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Optimasi database gagal: ' . $e->getMessage());
        }
    }

    /**
     * Restore from uploaded backup file
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restoreUploadBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Validate file
            $request->validate([
                'backup_file' => 'required|file|mimes:zip,sql,plain|max:50000',
            ]);

            // Create a temporary directory for extraction
            $tempDir = storage_path('app/temp-restore-' . time());
            if (!File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            $file = $request->file('backup_file');
            $extension = $file->getClientOriginalExtension();

            // Get database connection details
            $databaseName = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port', 3306);

            if ($extension === 'zip') {
                // Handle zip file
                $zipPath = $tempDir . '/backup.zip';
                $file->move($tempDir, 'backup.zip');

                // Extract the backup file
                $zip = new \ZipArchive();
                if ($zip->open($zipPath) === TRUE) {
                    $zip->extractTo($tempDir);
                    $zip->close();

                    // Look for SQL files
                    $sqlFiles = File::glob($tempDir . '/*.sql');

                    if (count($sqlFiles) > 0) {
                        // Import the SQL file using mysql command
                        $sqlFile = $sqlFiles[0]; // Use the first SQL file found

                        // Use mysql command to import
                        $command = "mysql --host={$host} --port={$port} --user={$username} --password='{$password}' {$databaseName} < {$sqlFile}";
                        exec($command, $output, $returnVar);

                        if ($returnVar !== 0) {
                            throw new \Exception('Database restore failed: ' . implode("\n", $output));
                        }
                    } else {
                        throw new \Exception('Tidak ada file SQL ditemukan dalam backup');
                    }
                } else {
                    throw new \Exception('Gagal membuka file backup');
                }
            } else {
                // Handle SQL file directly
                $sqlPath = $tempDir . '/backup.sql';
                $file->move($tempDir, 'backup.sql');

                // Use mysql command to import
                $command = "mysql --host={$host} --port={$port} --user={$username} --password='{$password}' {$databaseName} < {$sqlPath}";
                exec($command, $output, $returnVar);

                if ($returnVar !== 0) {
                    throw new \Exception('Database restore failed: ' . implode("\n", $output));
                }
            }

            // Clean up
            File::deleteDirectory($tempDir);

            // Log admin activity
            LogService::recordAdminActivity('restore', 'database', 'Restored from uploaded backup file');

            return redirect()->back()
                ->with('success', 'Restore dari file berhasil dilakukan!');
        } catch (\Exception $e) {
            // Clean up if temp directory exists
            if (isset($tempDir) && File::exists($tempDir)) {
                File::deleteDirectory($tempDir);
            }

            Log::error('Restore upload error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Restore gagal: ' . $e->getMessage());
        }
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}
