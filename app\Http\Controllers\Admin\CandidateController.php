<?php

namespace App\Http\Controllers\Admin;

use App\Export\CandidateExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\CandidateRequest;
use App\Models\Candidate;
use App\Models\CandidateCvView;
use App\Models\CandidateLanguage;
use App\Models\ContactInfo;
use App\Models\Education;
use App\Models\Experience;
use App\Models\JobRole;
use App\Models\Profession;
use App\Models\Setting;
use App\Models\Skill;
use App\Models\SkillTranslation;
use App\Models\User;
use App\Notifications\CandidateCreateApprovalPendingNotification;
use App\Notifications\CandidateCreateNotification;
use App\Notifications\UpdateCompanyPassNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Location\Entities\Country;
use App\Http\Controllers\Admin\CandidateDataTableController;

class CandidateController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            abort_if(! userCan('candidate.view'), 403);

            // Query dasar - gunakan subquery untuk menghindari masalah GROUP BY
            $candidatesQuery = Candidate::select('candidates.*')
                ->with(['user', 'education.translations'])
                ->join('users', 'candidates.user_id', '=', 'users.id')
                ->where('users.role', 'candidate'); // Pastikan hanya user dengan role 'candidate' yang ditampilkan

            // Tambahkan withCount setelah query dasar untuk menghindari konflik dengan join
            $candidatesQuery->withCount('appliedJobs');

            // Filter berdasarkan status
            if ($request->has('status')) {
                if ($request->status == 'active') {
                    $candidatesQuery->whereHas('user', function($query) {
                        $query->where('status', 1);
                    });
                } elseif ($request->status == 'inactive') {
                    $candidatesQuery->whereHas('user', function($query) {
                        $query->where('status', 0);
                    });
                }
            }

            // Filter berdasarkan sudah melamar
            if ($request->has('has_applied') && $request->has_applied == 'true') {
                // Gunakan distinct dan whereHas untuk menghindari duplikasi data
                $candidatesQuery->whereHas('appliedJobs', function($query) {
                    $query->whereNotNull('applied_jobs.id');
                });
            }

            // Filter berdasarkan kota/kabupaten
            if ($request->has('city') && !empty($request->city)) {
                $candidatesQuery->whereHas('user', function($query) use ($request) {
                    $query->where('kabupaten_kota', $request->city);
                });
            }

            // Filter berdasarkan jenis kelamin
            if ($request->has('gender') && !empty($request->gender)) {
                $candidatesQuery->where('candidates.gender', $request->gender);
            }

            // Filter berdasarkan pendidikan
            if ($request->has('education') && !empty($request->education)) {
                $candidatesQuery->where('candidates.education_id', $request->education);
            }

            // Filter berdasarkan keyword pencarian
            if ($request->has('keyword') && !empty($request->keyword)) {
                $keyword = $request->keyword;
                $candidatesQuery->whereHas('user', function($query) use ($keyword) {
                    $query->where('name', 'LIKE', "%{$keyword}%")
                        ->orWhere('email', 'LIKE', "%{$keyword}%")
                        ->orWhere('nik', 'LIKE', "%{$keyword}%")
                        ->orWhere('no_hp', 'LIKE', "%{$keyword}%");
                });
            }

            // Filter berdasarkan disabilitas
            if ($request->has('disabilitas') && $request->disabilitas !== '') {
                // Pastikan nilai disabilitas dikonversi ke integer untuk perbandingan yang benar
                $disabilitasValue = (int) $request->disabilitas;
                $candidatesQuery->where('candidates.disabilitas', $disabilitasValue);
            }

            // Urutkan berdasarkan waktu terbaru atau terlama
            if ($request->has('sort_by')) {
                if ($request->sort_by == 'oldest') {
                    $candidatesQuery->orderBy('candidates.created_at', 'asc');
                } else {
                    $candidatesQuery->orderBy('candidates.created_at', 'desc');
                }
            } else {
                $candidatesQuery->orderBy('candidates.created_at', 'desc');
            }

            // Pagination
            $candidates = $candidatesQuery->paginate(10)->appends(request()->query());

            // Ambil data untuk filter dropdown
            $cities = \App\Models\User::where('role', 'candidate')
                ->whereNotNull('kabupaten_kota')
                ->select('kabupaten_kota')
                ->distinct()
                ->orderBy('kabupaten_kota')
                ->pluck('kabupaten_kota')
                ->map(function($item) {
                    return (object)[
                        'id' => $item,
                        'name' => $item
                    ];
                });

            // Jika tidak ada data kota, tambahkan beberapa contoh
            if ($cities->isEmpty()) {
                $cities = collect([
                    (object)['id' => 'Kota Tangerang Selatan', 'name' => 'Kota Tangerang Selatan'],
                    (object)['id' => 'Kota Tangerang', 'name' => 'Kota Tangerang'],
                    (object)['id' => 'Kabupaten Tangerang', 'name' => 'Kabupaten Tangerang']
                ]);
            }

            // Ambil data pendidikan
            $educations = \App\Models\Education::with(['translations' => function($query) {
                $query->where('locale', app()->getLocale());
            }])->get()->map(function($education) {
                return (object)[
                    'id' => $education->id,
                    'name' => $education->translations->first() ? $education->translations->first()->name : ''
                ];
            })->filter(function($education) {
                return !empty($education->name);
            })->values();

            // Jika tidak ada data pendidikan, tambahkan beberapa contoh
            if ($educations->isEmpty()) {
                $educations = collect([
                    (object)['id' => 1, 'name' => 'SD/Sederajat'],
                    (object)['id' => 2, 'name' => 'SMP/Sederajat'],
                    (object)['id' => 3, 'name' => 'SMA/SMK/Sederajat'],
                    (object)['id' => 4, 'name' => 'D1/D2/D3'],
                    (object)['id' => 5, 'name' => 'D4/S1'],
                    (object)['id' => 6, 'name' => 'S2'],
                    (object)['id' => 7, 'name' => 'S3']
                ]);
            }

            // Hitung statistik untuk dashboard
            $totalCandidates = \App\Models\Candidate::join('users', 'candidates.user_id', '=', 'users.id')
                ->where('users.role', 'candidate')
                ->count();

            $activeCandidates = \App\Models\User::where('role', 'candidate')->where('status', 1)->count();
            $inactiveCandidates = \App\Models\User::where('role', 'candidate')->where('status', 0)->count();

            // Gunakan subquery untuk menghitung pencaker dengan lamaran
            $candidatesWithApplications = \App\Models\Candidate::join('users', 'candidates.user_id', '=', 'users.id')
                ->where('users.role', 'candidate')
                ->whereExists(function ($query) {
                    $query->select(\DB::raw(1))
                        ->from('applied_jobs')
                        ->whereRaw('applied_jobs.candidate_id = candidates.id');
                })->count();

            $disabilityCandidates = \App\Models\Candidate::join('users', 'candidates.user_id', '=', 'users.id')
                ->where('users.role', 'candidate')
                ->where('candidates.disabilitas', 1)
                ->count();

            return view('backend.candidate.index', compact(
                'candidates',
                'cities',
                'educations',
                'totalCandidates',
                'activeCandidates',
                'inactiveCandidates',
                'candidatesWithApplications',
                'disabilityCandidates'
            ));
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try {
            abort_if(! userCan('candidate.create'), 403);

            $data['countries'] = Country::all();
            $data['job_roles'] = JobRole::all()->sortBy('name');
            $data['professions'] = Profession::all()->sortBy('name');
            $data['experiences'] = Experience::all();
            $data['educations'] = Education::all();
            $data['skills'] = Skill::all()->sortBy('name');
            $data['candidate_languages'] = CandidateLanguage::all(['id', 'name']);
            $data['setting'] = Setting::first();

            return view('backend.candidate.create', $data);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function userCreate($request)
    {
        $request->validate([
            'username' => 'unique:users,username',
            'email' => 'required|email|unique:users,email',
            'nik' => 'nullable|digits_between:1,16|unique:users,nik',
            'no_hp' => 'nullable|unique:users,no_hp|max:14',
        ]);

        try {
            $password = $request->password ?? Str::random(8);

            $data = User::create([
                'role' => 'candidate',
                'name' => $request->name,
                'username' => Str::slug('K'.$request->name.'122'),
                'email' => $request->email,
                'email_verified_at' => now(),
                'password' => bcrypt($password),
                'remember_token' => Str::random(10),
                // Tambahkan data diri
                'no_hp' => $request->no_hp,
                'nik' => $request->nik,
                'alamat_ktp' => $request->alamat_ktp,
                'tempat_lahir' => $request->tempat_lahir,
                'tanggal_lahir' => $request->birth_date ? date('Y-m-d', strtotime($request->birth_date)) : null,
                'jenis_kelamin' => $request->gender == 'male' ? 'Laki-laki' : ($request->gender == 'female' ? 'Perempuan' : null),
                'agama' => $request->agama,
                'status_perkawinan' => $request->marital_status == 'married' ? 'Menikah' : 'Belum Menikah',
                // Tambahkan data lokasi
                'provinsi' => session('selectedStateId'),
                'kabupaten_kota' => session('selectedCityId'),
                'kecamatan' => session('selectedKecamatanId'),
                'kelurahan' => session('selectedKelurahanId'),
            ]);

            return [$password, $data];
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $data
     * @return \Illuminate\Http\Response
     */

    // public function candidateCreate($request, $data)
    // {
    //     $dateTime = Carbon::parse($request->birth_date);
    //     $date = $request['birth_date'] = $dateTime->format('Y-m-d H:i:s');

    //     // create candidate
    //     $candidate = Candidate::where('user_id', $data[1]->id)->first();
    //     $candidate->update([
    //         'role_id' => $request->role_id,
    //         'profession_id' => $request->profession_id,
    //         'experience_id' => $request->experience,
    //         'education_id' => $request->education,
    //         'gender' => $request->gender,
    //         'website' => $request->website,
    //         'bio' => $request->bio,
    //         'marital_status' => $request->marital_status,
    //         'birth_date' => $date,
    //     ]);

    //     // cv upload
    //     if ($request->cv) {
    //         $pdfPath = '/file/candidates/';
    //         $pdf = pdfUpload($request->cv, $pdfPath);
    //         $candidate->update(['cv' => $pdf]);
    //     }

    //     // image upload
    //     if ($request->image) {
    //         $path = 'images/candidates';
    //         $image = uploadImage($request->image, $path);
    //     } else {
    //         $image = createAvatar($data['name'], 'uploads/images/candidate');
    //     }

    //     $candidate->update(['photo' => $image]);

    //     // skills insert
    //     $skills = $request->skills;

    //     if ($skills) {
    //         $skillsArray = [];

    //         foreach ($skills as $skill) {
    //             $skill_exists = Skill::where('id', $skill)->orWhere('name', $skill)->first();

    //             if (! $skill_exists) {
    //                 $select_tag = Skill::create(['name' => $skill]);
    //                 array_push($skillsArray, $select_tag->id);
    //             } else {
    //                 array_push($skillsArray, $skill);
    //             }
    //         }

    //         $candidate->skills()->attach($skillsArray);
    //     }

    //     // languages insert
    //     $candidate->languages()->attach($request->languages);

    //     return $candidate;
    // }

    public function candidateCreate($request, $data)
    {
        try {
            $dateTime = Carbon::parse($request->birth_date);
            $date = $request['birth_date'] = $dateTime->format('Y-m-d H:i:s');

            // buat pelamar dari admin
            $name = $request->name ?? fake()->name();
            $candidate = Candidate::where('user_id', $data[1]->id)->first();
            // $candidate->update([
            $candidate->update([
                'role_id' => $request->role_id,
                'profession_id' => $request->profession_id,
                'experience_id' => $request->experience,
                'education_id' => $request->education,
                'gender' => $request->gender,
                'website' => $request->website,
                'bio' => $request->bio,
                'marital_status' => $request->marital_status,
                'birth_date' => $date,
                'disabilitas' => $request->disabilitas ?? 0,
                'jenis_disabilitas' => $request->disabilitas == 1 ? $request->jenis_disabilitas : null,
            ]);
            // lokasi
            updateMap($candidate);

            // unggah cv
            if ($request->cv) {
                $pdfPath = '/file/candidates/';
                $pdf = pdfUpload($request->cv, $pdfPath);
                $candidate->update(['cv' => $pdf]);
            }

            // unggah foto
            if ($request->image) {
                // $path = 'images/candidates';
                $path = 'uploads/images/candidates';

                $image = uploadImage($request->image, $path, [164, 164]);
            } else {
                $setDimension = [164, 164];
                $path = 'uploads/images/candidates';

                $image = createAvatar($name, $path, $setDimension);
                // $image = createAvatar($data['name'], 'uploads/images/candidate');
            }

            $candidate->update(['photo' => $image]);

            // masukkan skill/keterampilan
            $skills = $request->skills;

            if ($skills) {
                $skillsArray = [];

                foreach ($skills as $skill) {
                    // Cek apakah skill adalah ID atau nama
                    if (is_numeric($skill)) {
                        $skill_exists = Skill::find($skill);
                        if ($skill_exists) {
                            array_push($skillsArray, $skill_exists->id);
                        }
                    } else {
                        // Cari berdasarkan nama di tabel translations
                        $skill_translation = \App\Models\SkillTranslation::where('name', $skill)->first();

                        if ($skill_translation) {
                            array_push($skillsArray, $skill_translation->skill_id);
                        } else {
                            // Buat skill baru jika tidak ditemukan
                            $select_tag = Skill::create(['name' => $skill]);
                            array_push($skillsArray, $select_tag->id);
                        }
                    }
                }

                $candidate->skills()->attach($skillsArray);
            }

            // masukkan bahasa
            $candidate->languages()->attach($request->languages);

            return $candidate;
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(CandidateRequest $request)
    {
        abort_if(! userCan('candidate.create'), 403);
        $location = session()->get('location');
        if (! $location) {
            $request->validate(['location' => 'required']);
        }

        try {
            // Validasi file
            if ($request->image) {
                $request->validate(['image' => 'image|mimes:jpeg,png,jpg']);
            }
            if ($request->cv) {
                $request->validate(['cv' => 'mimetypes:application/pdf']);
            }
            if ($request->ak1) {
                $request->validate(['ak1' => 'mimes:jpeg,png,jpg,pdf|max:10240']);
            } else {
                $request->validate(['ak1' => 'required']);
            }

            $data = $this->userCreate($request);
            $this->candidateCreate($request, $data);
            $user = $data[1];
            $password = $data[0];

            // Upload KTP/AK1
            if ($request->hasFile('ak1')) {
                $ak1File = $request->file('ak1');
                $ak1Path = $ak1File->store('uploads/files/users/ak1', 'public');
                $user->update(['ak1' => $ak1Path]);
            }

            // jika SMTP sudah dikonfigurasi
            if (checkMailConfig()) {
                $candidate_account_auto_activation_enabled = Setting::where('candidate_account_auto_activation', 1)->count();

                // jika aktivasi pelamar diaktifkan, kirim email ke akun yang dibuat
                if ($candidate_account_auto_activation_enabled) {
                    Notification::route('mail', $user->email)->notify(new CandidateCreateNotification($user, $password));
                } else {
                    Notification::route('mail', $user->email)->notify(new CandidateCreateApprovalPendingNotification($user, $password));
                }
            }

            flashSuccess(__('candidate_created_successfully'));

            return redirect()->route('candidate.index');
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Tampilkan detail pelamar.
     *
     * @param  int  $candidate
     * @return \Illuminate\Http\Response
     */
    public function show($candidate)
    {
        try {
            // Cek izin akses
            abort_if(! userCan('candidate.view'), 403);

            // Ambil data kandidat dan user terkait
            $candidate = Candidate::with('skills', 'languages:id,name', 'profession')->findOrFail($candidate);

            try {
                $user = User::with('socialInfo', 'contactInfo')
                    ->where('role', 'candidate') // Pastikan hanya user dengan role 'candidate' yang dapat dilihat
                    ->findOrFail($candidate->user_id);
            } catch (\Exception $e) {
                // Log error untuk debugging
                \Log::error('User tidak ditemukan untuk candidate_id: ' . $candidate->id . ', user_id: ' . $candidate->user_id);
                \Log::error($e->getMessage());

                flashError('User terkait pencaker tidak ditemukan. Silakan hubungi administrator.');
                return redirect()->route('candidate.index');
            }

            // Ambil applied dan bookmark jobs
            $appliedJobs = $candidate->appliedJobs()->with('company.user', 'category', 'role')->get();
            $bookmarkJobs = $candidate->bookmarkJobs()->with('company.user', 'category', 'role')->get();

            // Kirim data user (termasuk alamat_ktp dan lainnya) langsung ke view
            return view('backend.candidate.show', compact('candidate', 'user', 'appliedJobs', 'bookmarkJobs'));
        } catch (\Exception $e) {
            // Tangani error jika terjadi
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Tampilkan form edit untuk pelamar.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Candidate $candidate)
    {
        try {
            abort_if(! userCan('candidate.update'), 403);

            // Periksa apakah user_id valid
            if (!$candidate->user_id) {
                // Coba perbaiki user_id jika tidak valid
                $possibleUser = User::where('email', 'like', '%' . $candidate->id . '%')
                    ->orWhere('name', 'like', '%' . $candidate->id . '%')
                    ->first();

                if ($possibleUser) {
                    // Update user_id jika ditemukan user yang mungkin terkait
                    $candidate->user_id = $possibleUser->id;
                    $candidate->save();

                    \Log::info('Berhasil memperbaiki user_id untuk candidate_id: ' . $candidate->id . ', user_id baru: ' . $possibleUser->id);
                } else {
                    flashError('Data pencaker tidak valid: user_id tidak ditemukan');
                    return redirect()->route('candidate.index');
                }
            }

            // Coba temukan user dengan try-catch untuk menangani error dengan lebih baik
            try {
                $user = User::with('contactInfo')
                    ->where('role', 'candidate') // Pastikan hanya user dengan role 'candidate' yang dapat diedit
                    ->findOrFail($candidate->user_id);
            } catch (\Exception $e) {
                // Log error untuk debugging
                \Log::error('User tidak ditemukan untuk candidate_id: ' . $candidate->id . ', user_id: ' . $candidate->user_id);
                \Log::error($e->getMessage());

                // Coba cari user berdasarkan email atau nama yang mungkin terkait dengan kandidat
                $possibleUser = User::where('role', 'candidate') // Pastikan hanya user dengan role 'candidate' yang dicari
                    ->where(function($query) use ($candidate) {
                        $query->where('email', 'like', '%' . $candidate->id . '%')
                            ->orWhere('name', 'like', '%' . $candidate->id . '%');
                    })
                    ->first();

                if ($possibleUser) {
                    // Update user_id jika ditemukan user yang mungkin terkait
                    $candidate->user_id = $possibleUser->id;
                    $candidate->save();

                    $user = $possibleUser;
                    \Log::info('Berhasil memperbaiki user_id untuk candidate_id: ' . $candidate->id . ', user_id baru: ' . $possibleUser->id);
                } else {
                    flashError('User terkait pencaker tidak ditemukan. Silakan hubungi administrator.');
                    return redirect()->route('candidate.index');
                }
            }

            $contactInfo = ContactInfo::where('user_id', $user->id)->first();

            // Jika contactInfo tidak ditemukan, buat baru
            if (!$contactInfo) {
                $contactInfo = ContactInfo::create([
                    'user_id' => $user->id,
                    'phone' => $user->no_hp ?? '',
                    'email' => $user->email ?? '',
                ]);
            }

            $job_roles = JobRole::all()->sortBy('name');
            $professions = Profession::all()->sortBy('name');
            $experiences = Experience::all();
            $educations = Education::all();
            $skills = Skill::all()->sortBy('name');
            $candidate_languages = CandidateLanguage::all(['id', 'name']);
            $candidate->load('skills', 'languages:id,name');
            $lat = $candidate->lat ? floatval($candidate->lat) : floatval(setting('default_lat'));
            $long = $candidate->long ? floatval($candidate->long) : floatval(setting('default_long'));

            return view('backend.candidate.edit', compact('contactInfo', 'candidate', 'user', 'job_roles', 'professions', 'experiences', 'educations', 'skills', 'candidate_languages', 'lat', 'long'));
        } catch (\Exception $e) {
            // Log error untuk debugging
            \Log::error('Error saat edit candidate: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            flashError('Terjadi Kesalahan: '.$e->getMessage());
            return back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */

    // public function update(Request $request, Candidate $candidate)
    // {
    //     abort_if(! userCan('candidate.update'), 403);

    //     $request->validate([
    //         'name' => 'required',
    //         'email' => 'required|email|unique:users,email,'.$candidate->user_id,
    //     ]);

    //     // user update
    //     $user = User::FindOrFail($candidate->user_id);
    //     $user->update([
    //         'name' => $request->name,
    //         'email' => $request->email,
    //     ]);

    //     // candidate update
    //     $candidate->update([
    //         'role_id' => $request->role_id,
    //         'profession_id' => $request->profession,
    //         'experience_id' => $request->experience,
    //         'education_id' => $request->education,
    //         'gender' => $request->gender,
    //         'website' => $request->website,
    //         'bio' => $request->bio,
    //         'marital_status' => $request->marital_status,
    //         'birth_date' => date('Y-m-d', strtotime($request->birth_date)),
    //     ]);

    //     // password change
    //     if ($request->password) {
    //         $request->validate([
    //             'password' => 'required',
    //         ]);
    //         $user->update([
    //             'password' => bcrypt($request->password),
    //         ]);
    //     }

    //     // image upload
    //     if ($request->image) {
    //         $request->validate([
    //             'image' => 'image|mimes:jpeg,png,jpg,gif',
    //         ]);

    //         $old_photo = $candidate->photo;
    //         if (file_exists($old_photo)) {
    //             if ($old_photo != 'backend/image/default.png') {
    //                 unlink($old_photo);
    //             }
    //         }
    //         $path = 'images/candidates';
    //         $image = uploadImage($request->image, $path);

    //         $candidate->update([
    //             'photo' => $image,
    //         ]);
    //     }
    //     // cv
    //     if ($request->cv) {
    //         $request->validate([
    //             'cv' => 'mimetypes:application/pdf',
    //         ]);
    //         $pdfPath = '/file/candidates/';
    //         $pdf = pdfUpload($request->cv, $pdfPath);

    //         $candidate->update([
    //             'cv' => $pdf,
    //         ]);
    //     }

    //     // Location
    //     updateMap($candidate);

    //     // skills
    //     $skills = $request->skills;

    //     if ($skills) {
    //         $skillsArray = [];

    //         foreach ($skills as $skill) {
    //             $skill_exists = SkillTranslation::where('skill_id', $skill)->orWhere('name', $skill)->first();

    //             if (! $skill_exists) {
    //                 $select_tag = Skill::create(['name' => $skill]);

    //                 $languages = loadLanguage();
    //                 foreach ($languages as $language) {
    //                     $select_tag->translateOrNew($language->code)->name = $skill;
    //                 }
    //                 $select_tag->save();

    //                 array_push($skillsArray, $select_tag->id);
    //             } else {
    //                 array_push($skillsArray, $skill_exists->id);
    //             }
    //         }
    //         $candidate->skills()->sync($request->skills);
    //     }

    //     // languages
    //     $candidate->languages()->sync($request->languages);

    //     if ($request->password) {
    //         // make Notification
    //         $data[] = $user;
    //         $data[] = $request->password;
    //         $data[] = 'Candidate';

    //         checkMailConfig() ? Notification::route('mail', $user->email)->notify(new UpdateCompanyPassNotification($data)) : '';
    //     }

    //     flashSuccess(__('candidate_updated_successfully'));

    //     return back();
    // }

    public function update(Request $request, Candidate $candidate)
    {
        try {
            abort_if(! userCan('candidate.update'), 403);

            $request->validate([
                'name' => 'required',
                'email' => 'required|email|unique:users,email,'.$candidate->user_id,
                'nik' => 'nullable|digits_between:1,16|unique:users,nik,'.$candidate->user_id,
                'no_hp' => 'nullable|max:14|unique:users,no_hp,'.$candidate->user_id,
            ]);

            // Perbarui data pengguna
            try {
                $user = User::findOrFail($candidate->user_id);

                $userData = [
                    'name' => $request->name,
                    'email' => $request->email,
                    // Tambahkan data diri
                    'no_hp' => $request->no_hp,
                    'nik' => $request->nik,
                    'alamat_ktp' => $request->alamat_ktp,
                    'tempat_lahir' => $request->tempat_lahir,
                    'tanggal_lahir' => $request->birth_date ? date('Y-m-d', strtotime($request->birth_date)) : null,
                    'jenis_kelamin' => $request->gender == 'male' ? 'Laki-laki' : ($request->gender == 'female' ? 'Perempuan' : null),
                    'agama' => $request->agama,
                    'status_perkawinan' => $request->marital_status == 'married' ? 'Menikah' : 'Belum Menikah',
                    // Tambahkan data lokasi
                    'provinsi' => session('selectedStateId'),
                    'kabupaten_kota' => session('selectedCityId'),
                    'kecamatan' => session('selectedKecamatanId'),
                    'kelurahan' => session('selectedKelurahanId'),
                ];

                // Jika password diisi, update password
                if ($request->password) {
                    $userData['password'] = bcrypt($request->password);
                }

                $user->update($userData);
            } catch (\Exception $e) {
                // Log error untuk debugging
                \Log::error('User tidak ditemukan untuk candidate_id: ' . $candidate->id . ', user_id: ' . $candidate->user_id);
                \Log::error($e->getMessage());

                flashError('User terkait pencaker tidak ditemukan. Silakan hubungi administrator.');
                return redirect()->route('candidate.index');
            }

            // Perbarui data pelamar
            $candidate->update([
                'role_id' => $request->role_id,
                'profession_id' => $request->profession,
                'experience_id' => $request->experience,
                'education_id' => $request->education,
                'gender' => $request->gender,
                'website' => $request->website,
                'bio' => $request->bio,
                'marital_status' => $request->marital_status,
                'birth_date' => date('Y-m-d', strtotime($request->birth_date)),
                'disabilitas' => $request->disabilitas ?? 0,
                'jenis_disabilitas' => $request->disabilitas == 1 ? $request->jenis_disabilitas : null,
            ]);

            // Ganti Password
            if ($request->password) {
                $request->validate([
                    'password' => 'required',
                ]);
                $user->update([
                    'password' => bcrypt($request->password),
                ]);
            }

            // Upload Foto
            if ($request->image) {
                $request->validate([
                    'image' => 'image|mimes:jpeg,png,jpg',
                ]);

                deleteImage($candidate->photo);

                $path = 'uploads/images/candidates';
                $image = uploadImage($request->image, $path, [164, 164]);

                $candidate->update([
                    'photo' => $image,
                ]);
            }

            // Upload CV
            if ($request->cv) {
                $request->validate([
                    'cv' => 'mimetypes:application/pdf',
                ]);
                $pdfPath = '/file/candidates/';
                $pdf = pdfUpload($request->cv, $pdfPath);

                $candidate->update([
                    'cv' => $pdf,
                ]);
            }

            // Upload AK1
            if ($request->hasFile('ak1')) {
                $request->validate([
                    'ak1' => 'file|mimes:jpeg,png,jpg,pdf|max:10240',
                ]);

                // Hapus file lama jika ada
                if ($user->ak1 && Storage::exists('public/' . $user->ak1)) {
                    Storage::delete('public/' . $user->ak1);
                }

                // Simpan file baru
                $ak1Path = $request->file('ak1')->store('uploads/files/users/ak1', 'public');
                $user->update(['ak1' => $ak1Path]);
            }

            // Lokasi
            updateMap($candidate);

            // Keterampilan/Skills
            $skills = $request->skills;

            if ($skills) {
                $skillsArray = [];

                foreach ($skills as $skill) {
                    // Cek apakah skill adalah ID atau nama
                    if (is_numeric($skill)) {
                        $skill_exists = Skill::find($skill);
                        if ($skill_exists) {
                            array_push($skillsArray, $skill_exists->id);
                        }
                    } else {
                        // Cari berdasarkan nama di tabel translations
                        $skill_translation = \App\Models\SkillTranslation::where('name', $skill)->first();

                        if ($skill_translation) {
                            array_push($skillsArray, $skill_translation->skill_id);
                        } else {
                            // Buat skill baru jika tidak ditemukan
                            $select_tag = Skill::create(['name' => $skill]);

                            $languages = loadLanguage();
                            foreach ($languages as $language) {
                                $select_tag->translateOrNew($language->code)->name = $skill;
                            }
                            $select_tag->save();

                            array_push($skillsArray, $select_tag->id);
                        }
                    }
                }
                $candidate->skills()->sync($skillsArray);
            }

            // Bahasa
            $candidate->languages()->sync($request->languages);

            if ($request->password) {
                // Buat notifikasi ke pelamar
                $data[] = $user;
                $data[] = $request->password;
                $data[] = 'Candidate';

                checkMailConfig() ? Notification::route('mail', $user->email)->notify(new UpdateCompanyPassNotification($data)) : '';
            }

            flashSuccess(__('candidate_updated_successfully'));

            return back();
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Hapus data pelamar dari database.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            abort_if(! userCan('candidate.delete'), 403);

            // Cari kandidat berdasarkan ID
            $candidate = Candidate::findOrFail($id);

            // Hapus relasi terlebih dahulu
            $candidate->skills()->detach();
            $candidate->languages()->detach();
            $candidate->bookmarkJobs()->detach();

            // Hapus lamaran pekerjaan
            $candidate->appliedJobs()->detach();

            // Hapus view CV
            CandidateCvView::query()
                ->where('candidate_id', $candidate->id)
                ->delete();

            // Hapus file CV jika ada
            if ($candidate->cv && file_exists(public_path($candidate->cv))) {
                @unlink(public_path($candidate->cv));
            }

            // Hapus foto jika ada dan bukan default
            if ($candidate->photo && file_exists(public_path($candidate->photo)) && $candidate->photo != 'backend/image/default.png') {
                @unlink(public_path($candidate->photo));
            }

            // Hapus user terkait
            if ($candidate->user_id) {
                $user = User::find($candidate->user_id);
                if ($user) {
                    $user->delete();
                }
            }

            // Hapus kandidat
            $candidate->delete();

            flashSuccess('Pencaker berhasil dihapus');

            return redirect()->route('candidate.index');
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Ubah status pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function statusChange(Request $request)
    {
        try {
            $user = User::findOrFail($request->id);
            $user->status = $request->status;
            $user->save();

            if ($request->status == 1) {
                return responseSuccess(__('candidate_activated_successfully'));
            } else {
                return responseSuccess(__('candidate_deactivated_successfully'));
            }
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Ubah status verifikasi pelamar
     *
     * @return \Illuminate\Http\Response
     */
    public function verificationChange(Request $request)
    {
        try {
            $user = User::findOrFail($request->id);

            if ($request->status) {
                $user->update(['email_verified_at' => now()]);
                $message = __('email_verified_successfully');
            } else {
                $user->update(['email_verified_at' => null]);
                $message = __('email_unverified_successfully');
            }

            return responseSuccess($message);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    public function candidateExport($type)
    {
        try {
            $filename = 'daftar_pencaker_' . date('d-m-Y') . '.' . $type;

            if ($type == 'pdf') {
                // Periksa apakah class MPDF tersedia
                if (class_exists('Mpdf\Mpdf')) {
                    return Excel::download(new CandidateExport(), $filename, \Maatwebsite\Excel\Excel::MPDF);
                }
                // Periksa apakah class DOMPDF tersedia
                elseif (class_exists('Dompdf\Dompdf')) {
                    return Excel::download(new CandidateExport(), $filename, \Maatwebsite\Excel\Excel::DOMPDF);
                }
                // Jika tidak ada package PDF yang tersedia
                else {
                    // Tampilkan pesan untuk pengguna
                    flashWarning('Untuk menggunakan ekspor PDF, jalankan perintah: composer require mpdf/mpdf atau composer require barryvdh/laravel-dompdf');

                    // Gunakan HTML sebagai fallback (akan membuka di browser)
                    return response()->streamDownload(function() {
                        $candidates = Candidate::with(['user', 'education', 'profession'])
                            ->orderBy('created_at', 'desc')
                            ->get();

                        echo view('exports.candidates', compact('candidates'));
                    }, $filename);
                }
            } else {
                // Untuk ekspor Excel (xlsx)
                return Excel::download(new CandidateExport(), $filename);
            }
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());
            return back();
        }
    }

    /**
     * Bulk activate candidates
     */
    public function bulkActivate(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'required|integer|exists:users,id'
        ]);

        User::whereIn('id', $request->user_ids)->update(['status' => 1]);

        return response()->json(['message' => 'Semua pencaker terpilih berhasil diaktifkan!']);
    }

    /**
     * Bulk deactivate candidates
     */
    public function bulkDeactivate(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'required|integer|exists:users,id'
        ]);

        User::whereIn('id', $request->user_ids)->update(['status' => 0]);

        return response()->json(['message' => 'Semua pencaker terpilih berhasil dinonaktifkan!']);
    }

    /**
     * Bulk delete candidates
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'candidate_ids' => 'required|array',
            'candidate_ids.*' => 'required|integer|exists:candidates,id'
        ]);

        $candidates = Candidate::whereIn('id', $request->candidate_ids)->get();

        foreach ($candidates as $candidate) {
            // Delete user
            if ($candidate->user) {
                $candidate->user->delete();
            }

            // Delete candidate
            $candidate->delete();
        }

        return response()->json(['message' => 'Semua pencaker terpilih berhasil dihapus!']);
    }

    /**
     * Display a listing of candidates with AK1 documents
     */
    public function ak1List()
    {
        try {
            // Cek izin akses
            abort_if(! userCan('candidate.view'), 403);

            // Get candidates with AK1 documents
            $candidates = User::where('role', 'candidate')
                ->whereNotNull('ak1')
                ->with('candidate')
                ->paginate(10);

            return view('backend.candidate.ak1', compact('candidates'));
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Download AK1 document
     */
    public function downloadAK1($id)
    {
        try {
            // Cek izin akses
            abort_if(! userCan('candidate.view'), 403);

            // Get candidate
            $user = User::findOrFail($id);

            if (!$user->ak1) {
                flashError('Dokumen AK1 tidak ditemukan');
                return back();
            }

            $filePath = storage_path('app/public/' . $user->ak1);

            if (!file_exists($filePath)) {
                flashError('File tidak ditemukan di server');
                return back();
            }

            $fileName = 'AK1_' . $user->name . '_' . date('Y-m-d') . '.' . pathinfo($filePath, PATHINFO_EXTENSION);

            return response()->download($filePath, $fileName);
        } catch (\Exception $e) {
            flashError('Terjadi Kesalahan: '.$e->getMessage());
            return back();
        }
    }
}
