<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Website\MessageController;
use App\Models\AppliedJob;
use App\Models\Candidate;
use App\Models\Job;
use App\Models\Message;
use App\Models\MessageThread;
use App\Models\Messenger;
use App\Models\MessengerUser;
use App\Models\User;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MessengerController extends Controller
{
    public function companyMessages()
    {
        // Redirect ke sistem pesan baru
        return redirect()->route('company.inbox');
    }

    public function candidateMessages()
    {
        // Redirect ke sistem pesan baru
        return redirect()->route('candidate.inbox');
    }

    public function fetchMessages($username)
    {
        $user = User::whereUsername($username)->firstOrFail();

        if ($user->id != auth()->id()) {
            Messenger::where(function ($query) use ($user) {
                $query->where(function ($q) use ($user) {
                    $q->where('from', auth()->id());
                    $q->where('to', $user->id);
                })
                    ->orWhere(function ($q) use ($user) {
                        $q->where('to', auth()->id());
                        $q->where('from', $user->id);
                    });
            })
                ->update(['read' => 1]);
        }

        return Messenger::where(function ($query) use ($user) {
            $query->where(function ($q) use ($user) {
                $q->where('from', auth()->id());
                $q->where('to', $user->id);
            })
                ->orWhere(function ($q) use ($user) {
                    $q->where('to', auth()->id());
                    $q->where('from', $user->id);
                });
        })
            ->get();

        // return Messenger::where(function ($query) use ($user) {
        //     $query->where(function ($q) use ($user) {
        //         $q->where('from', auth()->id());
        //         $q->where('to', $user->id);
        //     })
        //         ->orWhere(function ($q) use ($user) {
        //             $q->where('to', auth()->id());
        //             $q->where('from', $user->id);
        //         });
        // })
        // ->latest()
        // ->get()
        // ->groupBy(function ($message) {
        //     return $message->created_at->format('d M, Y');
        // });
    }

    public function sendMessage(Request $request)
    {
        try {
            $request->validate(['message' => 'required']);

            $message = Messenger::create([
                'from' => auth()->id(),
                'to' => $request->to,
                'body' => $request->message ?? 'No message',
                'messenger_user_id' => $request->chat_id,
                'read' => 0,
            ]);

            // Store typing status in cache
            Cache::put('user_' . auth()->id() . '_typing_to_' . $request->to, false, 60);

            // Load the message with sender and receiver
            $message->load('sender', 'receiver');

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Check for new messages (polling)
     */
    public function checkNewMessages(Request $request)
    {
        try {
            $request->validate([
                'last_message_id' => 'required|integer',
                'chat_with' => 'required|integer',
            ]);

            $lastMessageId = $request->last_message_id;
            $chatWith = $request->chat_with;

            // Get new messages
            $newMessages = Messenger::where('id', '>', $lastMessageId)
                ->where(function($query) use ($chatWith) {
                    $query->where(function($q) use ($chatWith) {
                        $q->where('from', auth()->id());
                        $q->where('to', $chatWith);
                    })->orWhere(function($q) use ($chatWith) {
                        $q->where('to', auth()->id());
                        $q->where('from', $chatWith);
                    });
                })
                ->get();

            // Mark messages as read
            if ($newMessages->count() > 0) {
                Messenger::where('to', auth()->id())
                    ->where('from', $chatWith)
                    ->where('read', 0)
                    ->update(['read' => 1]);
            }

            // Check if user is typing
            $isTyping = Cache::get('user_' . $chatWith . '_typing_to_' . auth()->id(), false);

            return response()->json([
                'success' => true,
                'new_messages' => $newMessages,
                'is_typing' => $isTyping
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function messageSendCandidate(Request $request)
    {
        // Redirect ke sistem pesan baru
        $messageController = new MessageController();
        return $messageController->companyMessageCandidate($request);
    }

    public function loadUnreadMessageCount()
    {
        if (auth()->check()) {
            $user = auth()->user();
            $count = 0;

            if ($user->role == 'company') {
                $count = Message::whereHas('thread', function ($q) {
                    $q->where('company_id', currentCompany()->id);
                })->where('receiver_id', $user->id)
                  ->where('read', false)
                  ->count();
            } elseif ($user->role == 'candidate') {
                $count = Message::whereHas('thread', function ($q) {
                    $q->where('candidate_id', currentCandidate()->id);
                })->where('receiver_id', $user->id)
                  ->where('read', false)
                  ->count();
            } elseif ($user->role == 'admin') {
                $count = Message::where('receiver_id', $user->id)
                              ->where('read', false)
                              ->count();
            }

            return $count;
        } else {
            return 0;
        }
    }

    public function syncUserList()
    {
        $role = auth()->user()->role;

        if ($role == 'company') {
            $users = $this->fetchCompanyUserList();
        } elseif ($role == 'candidate') {
            $users = $this->fetchCandidateUserList();
        }

        return $users ?? [];
    }

    protected function fetchCompanyUserList()
    {
        $applied_jobs = AppliedJob::with('applicationGroup:id,name')->get();
        $all_users = MessengerUser::whereHas('messages')->with('candidate', 'job:id,title,slug')
            ->where('company_id', currentCompany()->id)
            ->withCount(['messages as latest_message_time' => function ($query) {
                $query->select(\DB::raw('max(created_at)'));
            }])
            ->orderByDesc('latest_message_time')
            ->get();

        $users = $all_users->unique('candidate_id')->map(function ($user) use ($applied_jobs) {
            $applied_job = $applied_jobs->where('candidate_id', $user->candidate_id)->where('job_id', $user->job_id)->first();

            $last_message = Messenger::candidateMessages($user)->latest()->first();

            if ($last_message) {
                $user->latest_message = $last_message->body;
                $diff_time = $last_message->created_at->diffForHumans(now(), CarbonInterface::DIFF_RELATIVE_AUTO, true, 1);
                $user->latest_message_humans_time = Str::of($diff_time)->replace(['from now', 'before'], '')->trim();

                $user->last_message_from_me = $last_message->from == auth()->id();
            }
            $user->unread_count = $this->getUnreadMessageCount($user->candidate_id);
            $user->application_status = $applied_job->applicationGroup->name ?? 'Semua Lamaran';

            return $user;
        });

        return $users;
    }

    protected function fetchCandidateUserList()
    {
        $all_users = MessengerUser::whereHas('messages')->with('company', 'job:id,title,slug')
            ->where('candidate_id', currentCandidate()->id)
            ->withCount(['messages as latest_message_time' => function ($query) {
                $query->select(\DB::raw('max(created_at)'));
            }])
            ->orderByDesc('latest_message_time')
            ->get();

        $users = $all_users->unique('company_id')->map(function ($user) {
            $last_message = Messenger::companyMessages($user)->latest()->first();

            if ($last_message) {
                $user->latest_message = $last_message->body;
                $diff_time = $last_message->created_at->diffForHumans(now(), CarbonInterface::DIFF_RELATIVE_AUTO, true, 1);
                $user->latest_message_humans_time = Str::of($diff_time)->replace(['before', 'after'], '')->trim();

                $user->last_message_from_me = $last_message->from == auth()->id();
            }

            $user->unread_count = $this->getUnreadMessageCount($user->company_id);

            return $user;
        });

        return $users;
    }

    public function filterUsers(Request $request)
    {
        if ($request->role == 'company') {
            $applied_jobs = AppliedJob::with('applicationGroup:id,name')->get();
            $all_users = MessengerUser::whereHas('messages')
                ->with('candidate', 'job:id,title,slug')
                ->where('company_id', currentCompany()->id)
                ->when($request->job, function ($query) use ($request) {
                    $query->where('job_id', $request->job);
                })
                ->withCount(['messages as latest_message_time' => function ($query) {
                    $query->select(\DB::raw('max(created_at)'));
                }])
                ->orderByDesc('latest_message_time')
                ->get();

            $users = $all_users->unique('candidate_id')
                ->map(function ($user) use ($applied_jobs) {
                    $applied_job = $applied_jobs->where('candidate_id', $user->candidate_id)
                        ->where('job_id', $user->job_id)
                        ->first();

                    $user->unread_count = Messenger::candidateMessages($user)->where('read', '!=', 1)->count() ?? 0;
                    $user->application_status = $applied_job->applicationGroup->name ?? 'Semua Lamaran';
                    $last_message = Messenger::select('id', 'from', 'to', 'body', 'created_at')->where(function ($query) use ($user) {
                        $query->where(function ($q) use ($user) {
                            $q->where('from', auth()->id());
                            $q->where('to', $user->candidate->user_id);
                        })->orWhere(function ($q) use ($user) {
                            $q->where('to', auth()->id());
                            $q->where('from', $user->candidate->user_id);
                        });
                    })
                        ->latest()
                        ->first();

                    if ($last_message) {
                        $user->latest_message = Str::limit($last_message->body, 15);
                        $diff_time = $last_message->created_at->diffForHumans(now(), CarbonInterface::DIFF_RELATIVE_AUTO, true, 1);
                        $user->latest_message_humans_time = Str::of($diff_time)->replace(['from now', 'before'], '')->trim();
                        $user->last_message_from_me = $last_message->from == auth()->id();
                    }

                    return $user;
                });

            return $users;
        } else {
            $applied_jobs = AppliedJob::with('applicationGroup:id,name')->get();
            $all_users = MessengerUser::whereHas('messages')
                ->with('company', 'job:id,title,slug')
                ->where('candidate_id', currentCandidate()->id)
                ->when($request->job, function ($query) use ($request) {
                    $query->where('job_id', $request->job);
                })
                ->withCount(['messages as latest_message_time' => function ($query) {
                    $query->select(\DB::raw('max(created_at)'));
                }])
                ->orderByDesc('latest_message_time')
                ->get()
                ->map(function ($user) use ($applied_jobs) {
                    $applied_job = $applied_jobs->where('candidate_id', $user->candidate_id)
                        ->where('job_id', $user->job_id)->first();
                    $user->application_status = $applied_job->applicationGroup->name ?? 'Semua Lamaran';

                    return $user;
                });

            $users = $all_users->unique('company_id');

            return $users;
        }
    }

    protected function getUnreadMessageCount($id)
    {
        $auth_user = auth()->user();

        if ($auth_user->role == 'company') {
            $user_messenger_id = MessengerUser::where('company_id', currentCompany()->id)->where('candidate_id', $id)->value('id');

            $unread_count = Messenger::where('messenger_user_id', $user_messenger_id)
                ->where('to', auth()->id())
                ->where('read', '!=', 1)
                ->count();
        } else {
            $user_messenger_id = MessengerUser::where('company_id', $id)->where('candidate_id', currentCandidate()->id)->value('id');

            $unread_count = Messenger::where('messenger_user_id', $user_messenger_id)
                ->where('to', auth()->id())
                ->where('read', '!=', 1)
                ->count();
        }

        return $unread_count ?? 0;
    }

    /**
     * Send typing indicator
     */
    public function typingIndicator(Request $request)
    {
        try {
            $request->validate([
                'to' => 'required',
                'typing' => 'required|boolean',
            ]);

            // Store typing status in cache
            Cache::put('user_' . auth()->id() . '_typing_to_' . $request->to, $request->typing, 60);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Mark message as read
     */
    public function markMessageRead(Request $request)
    {
        try {
            $request->validate([
                'message_id' => 'required',
            ]);

            $message = Messenger::find($request->message_id);

            if ($message && $message->to == auth()->id()) {
                $message->update(['read' => 1]);
                return response()->json(['success' => true]);
            }

            return response()->json(['success' => false, 'message' => 'Message not found or unauthorized']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Send attachment message
     */
    public function sendAttachment(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|max:10240', // Max 10MB
                'to' => 'required',
                'chat_id' => 'required',
            ]);

            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();

            // Store file
            $path = $file->storeAs('messenger_attachments', $fileName, 'public');
            $fileUrl = asset('public/storage/' . $path);

            // Determine file type
            $fileType = $this->getFileType($file->getClientOriginalExtension());

            // Create message with attachment
            $message = Messenger::create([
                'from' => auth()->id(),
                'to' => $request->to,
                'body' => "[{$fileType}] " . $file->getClientOriginalName(),
                'messenger_user_id' => $request->chat_id,
                'read' => 0,
                'attachment' => [
                    'url' => $fileUrl,
                    'type' => $fileType,
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                ],
            ]);

            // Load the message with sender and receiver
            $message->load('sender', 'receiver');

            // Store typing status in cache (set to false after sending a message)
            Cache::put('user_' . auth()->id() . '_typing_to_' . $request->to, false, 60);

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get file type based on extension
     */
    private function getFileType($extension)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

        if (in_array(strtolower($extension), $imageExtensions)) {
            return 'image';
        } elseif (in_array(strtolower($extension), $documentExtensions)) {
            return 'document';
        } else {
            return 'file';
        }
    }

    /**
     * Mark messages as read for a specific username
     */
    public function messageMarkasRead($username)
    {
        try {
            $user = User::whereUsername($username)->firstOrFail();

            if ($user->id != auth()->id()) {
                Messenger::where(function ($query) use ($user) {
                    $query->where(function ($q) use ($user) {
                        $q->where('to', auth()->id());
                        $q->where('from', $user->id);
                    });
                })
                ->where('read', 0)
                ->update(['read' => 1]);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}
