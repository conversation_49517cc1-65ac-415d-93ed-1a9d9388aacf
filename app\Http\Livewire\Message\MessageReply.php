<?php

namespace App\Http\Livewire\Message;

use App\Models\AppliedJob;
use App\Models\Message;
use App\Models\MessageThread;
use Livewire\Component;
use Livewire\WithFileUploads;

class MessageReply extends Component
{
    use WithFileUploads;

    public $threadId = null;
    public $message = '';
    public $attachment;
    public $canReply = true;

    protected $listeners = [
        'threadSelected' => 'setThread',
    ];

    public function setThread($threadId)
    {
        $this->threadId = $threadId;
        $this->resetForm();
        $this->checkReplyPermission();
    }

    public function resetForm()
    {
        $this->message = '';
        $this->attachment = null;
    }

    public function checkReplyPermission()
    {
        if (!$this->threadId) {
            $this->canReply = false;
            return;
        }

        $thread = MessageThread::with(['messages' => function ($q) {
            $q->latest();
        }])->find($this->threadId);

        if (!$thread) {
            $this->canReply = false;
            return;
        }

        $user = auth()->user();
        $lastMessage = $thread->messages()->latest()->first();

        // Jika pesan terakhir tidak dapat dibalas
        if ($lastMessage && !$lastMessage->can_reply && $user->role != 'admin') {
            $this->canReply = false;
            return;
        }

        // Pencaker selalu bisa membalas pesan dari perusahaan
        if ($user->role == 'candidate' && $thread->company_id) {
            $this->canReply = true;
            // Log untuk debugging
            \Log::info('Thread ID: ' . $thread->id . ', Candidate can reply to company message');
        }

        // Perusahaan tidak bisa mengirim pesan ke pencaker kecuali pencaker melamar pekerjaannya
        if ($user->role == 'company' && $thread->candidate_id) {
            $hasApplied = AppliedJob::where('candidate_id', $thread->candidate_id)
                ->whereHas('job', function($query) {
                    $query->where('company_id', currentCompany()->id);
                })
                ->exists();

            if (!$hasApplied) {
                $this->canReply = false;
                return;
            }
        }

        // Admin hanya bisa mengirim pesan jika:
        // 1. Thread dimulai oleh admin (is_admin_thread = true)
        // 2. Atau admin sudah pernah mengirim pesan di thread ini
        if ($user->role == 'admin') {
            if ($thread->is_admin_thread) {
                $this->canReply = true;
                return;
            }

            // Cek apakah admin sudah pernah mengirim pesan di thread ini
            $adminHasSentMessage = $thread->messages()
                ->where('sender_id', $user->id)
                ->exists();

            if (!$adminHasSentMessage) {
                // Admin belum pernah mengirim pesan di thread ini
                $this->canReply = false;
                return;
            }
        }

        $this->canReply = true;
    }

    public function sendMessage()
    {
        if (!$this->threadId) {
            return;
        }

        $this->validate([
            'message' => 'required_without:attachment',
            'attachment' => 'nullable|file|max:5120', // Maksimal 5MB
        ]);

        $thread = MessageThread::findOrFail($this->threadId);
        $user = auth()->user();

        // Tentukan penerima pesan
        $receiverId = null;
        if ($user->role == 'company' && $thread->candidate_id) {
            $receiverId = $thread->candidate->user_id;
        } elseif ($user->role == 'candidate' && $thread->company_id) {
            $receiverId = $thread->company->user_id;
        } elseif ($user->role == 'admin') {
            if ($thread->company_id) {
                $receiverId = $thread->company->user_id;
            } elseif ($thread->candidate_id) {
                $receiverId = $thread->candidate->user_id;
            }
        }

        $messageData = [
            'message_thread_id' => $thread->id,
            'sender_id' => $user->id,
            'receiver_id' => $receiverId,
            'body' => $this->message,
            'type' => 'umum', // Pengguna biasa hanya bisa mengirim pesan umum
            'can_reply' => true, // Pengguna biasa tidak bisa mengatur can_reply
            'read' => false,
        ];

        // Proses lampiran jika ada
        if ($this->attachment) {
            $fileName = time() . '_' . $this->attachment->getClientOriginalName();
            $path = $this->attachment->storeAs('message_attachments', $fileName, 'public');
            $fileUrl = asset('storage/' . $path);

            // Tentukan jenis file
            $fileType = $this->getFileType($this->attachment->getClientOriginalExtension());

            $messageData['attachment'] = [
                'url' => $fileUrl,
                'type' => $fileType,
                'name' => $this->attachment->getClientOriginalName(),
                'size' => $this->attachment->getSize(),
            ];

            if (empty($messageData['body'])) {
                $messageData['body'] = "[{$fileType}] " . $this->attachment->getClientOriginalName();
            }
        }

        Message::create($messageData);

        $this->resetForm();
        $this->emit('messageSent');
    }

    private function getFileType($extension)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'];

        if (in_array(strtolower($extension), $imageExtensions)) {
            return 'image';
        } elseif (in_array(strtolower($extension), $documentExtensions)) {
            return 'document';
        } else {
            return 'file';
        }
    }

    public function render()
    {
        return view('livewire.message.message-reply');
    }
}
