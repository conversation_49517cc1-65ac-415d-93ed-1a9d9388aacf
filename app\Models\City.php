<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class City extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'state_id', 'long', 'lat'];
    protected $guarded = [];

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function jobs()
    {
        return $this->hasMany(Job::class);
    }

    public function kecamatan() // Sesuaikan nama metode untuk mencocokkan dengan model Kecamatan
    {
        return $this->hasMany(Kecamatan::class, 'city_id'); // Menyebutkan relasi ke model Kecamatan
    }
}
