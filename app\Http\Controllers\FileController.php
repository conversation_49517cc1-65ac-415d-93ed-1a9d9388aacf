<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class FileController extends Controller
{
    public function upload(Request $request)
    {
        // Validasi input
        $request->validate([
            'file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120', // Maksimum ukuran file dalam KB (5120 KB = 5 MB)
        ]);

        // Proses upload
        $file = $request->file('file');
        $filename = time() . '_' . $file->getClientOriginalName();
        $file->move(public_path('uploads'), $filename);

        return response()->json(['filepath' => 'uploads/' . $filename]);
    }
}
