<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class EnsureCandidateExperiencesColumns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Ensure the table exists
        if (!Schema::hasTable('candidate_experiences')) {
            return;
        }

        // Check and add missing columns
        Schema::table('candidate_experiences', function (Blueprint $table) {
            // Add currently_working if it doesn't exist
            if (!Schema::hasColumn('candidate_experiences', 'currently_working')) {
                $table->boolean('currently_working')->default(false)->after('responsibilities');
            }
            
            // Add fresh_graduate if it doesn't exist
            if (!Schema::hasColumn('candidate_experiences', 'fresh_graduate')) {
                $table->boolean('fresh_graduate')->default(false)->after('currently_working');
            }
        });

        // Update existing records to ensure data consistency
        DB::statement("UPDATE candidate_experiences SET currently_working = 0 WHERE currently_working IS NULL");
        DB::statement("UPDATE candidate_experiences SET fresh_graduate = 0 WHERE fresh_graduate IS NULL");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration is for ensuring columns exist, so we don't drop them in down()
        // as they might be needed by other parts of the application
    }
}
