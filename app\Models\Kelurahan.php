<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class <PERSON><PERSON><PERSON><PERSON> extends Model
{
    use HasFactory;

    protected $table = 'kelurahan';
    protected $fillable = ['name', 'kecamatan_id', 'long', 'lat'];
    protected $guarded = [];

    public function kecamatan()
    {
        return $this->belongsTo(Kecamatan::class, 'kecamatan_id'); // Relasi dengan model Kecamatan
    }

    public function jobs()
    {
        return $this->hasMany(Job::class); // Jika ada relasi dengan peker<PERSON>
    }
}
