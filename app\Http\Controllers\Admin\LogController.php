<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminLog;
use App\Models\LoginLog;
use Illuminate\Http\Request;

class LogController extends Controller
{
    /**
     * Display login logs
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function loginLogs(Request $request)
    {
        $logs = LoginLog::where('user_type', 'admin')
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('backend.logs.login-logs', compact('logs'));
    }

    /**
     * Display admin activity logs
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function activityLogs(Request $request)
    {
        $logs = AdminLog::with('admin')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('backend.logs.activity-logs', compact('logs'));
    }
}
