<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CandidateResume extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'file_size',
        'file_url',
    ];

    /**
     * Get the human-readable file size of the candidate's resume.
     *
     * @return string The file size with appropriate units (e.g., KB, MB).
     */
    public function getFileSizeAttribute()
    {
        return get_file_size($this->file);
    }

    /**
     * Get the URL of the candidate's resume file.
     *
     * @return string The URL of the resume file.
     */
    public function getFileUrlAttribute()
    {
        $file = $this->file;

        return asset($file);
    }
}
