<?php

namespace App\Services\Website\Candidate;

use App\Models\User;
use Carbon\Carbon;

class CandidateProfileDetailsService
{
    /**
     * Get candidate profile details
     */
    public function execute($request): array
    {
        $user = authUser();
        // $user_plan = $user->company->userPlan;

        $candidate = User::where('username', $request->username)
            ->with(['contactInfo', 'socialInfo', 'candidate' => function ($query) {
                $query->with('experience', 'education', 'experiences', 'educations', 'profession', 'languages:id,name', 'skills', 'socialInfo')
                    ->withCount(['bookmarkCandidates as bookmarked' => function ($q) {
                        $q->where('company_id', currentCompany()->id);
                    }])
                    ->withCount(['already_views as already_view' => function ($q) {
                        $q->where('company_id', currentCompany()->id);
                    }]);
            }])->firstOrFail();

            $candidate->candidate->birth_date = date('d-m-Y', strtotime($candidate->candidate->birth_date));

        // Removed CV view limitation logic

        $languages = $candidate->candidate
            ->languages()
            ->pluck('name')
            ->toArray();
        $candidate_languages = $languages ? implode(', ', $languages) : '';

        $skills = $candidate->candidate->skills->pluck('name');
        $candidate_skills = $skills ? implode(', ', json_decode(json_encode($skills), true)) : '';

        return [
            'success' => true,
            'data' => $candidate,
            'skills' => $candidate_skills ?? '',
            'languages' => $candidate_languages ?? '',
        ];
    }
}
