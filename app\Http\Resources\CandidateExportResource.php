<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CandidateExportResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'nik' => $this->user->nik ?? '-',
            'Name' => $this->user->name ?? '-',
            'Email' => $this->user->email ?? '-',
            'Gender' => $this->gender ?? '-',
            'Number' => $this->no_hp ?? '-',
            'Address' => $this->user->alamat_ktp ?? '-',
        ];
    }
}
